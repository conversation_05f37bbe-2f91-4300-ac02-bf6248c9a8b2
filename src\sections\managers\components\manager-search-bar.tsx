import { useState, useEffect } from 'react';
import {
  Autocomplete,
  TextField,
  InputAdornment,
  SxProps,
  Theme,
  Typography,
  Box,
  ListItem,
  ListItemText,
  Avatar,
  AutocompleteChangeReason
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import parse from 'autosuggest-highlight/parse';
import match from 'autosuggest-highlight/match';

interface ManagerSearchBarProps {
  query: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  sx?: SxProps<Theme>;
  options?: ManagerOption[];
  onOptionSelect?: (option: ManagerOption | null) => void;
}

export interface ManagerOption {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  avatarUrl?: string;
  status?: string;
  role?: string;
}

export default function ManagerSearchBar({
  query,
  onChange,
  placeholder = 'Search managers...',
  sx,
  options = [],
  onOptionSelect,
}: ManagerSearchBarProps) {
  const [inputValue, setInputValue] = useState(query);
  const [selectedOption, setSelectedOption] = useState<ManagerOption | null>(null);

  // Update local state when query prop changes
  useEffect(() => {
    setInputValue(query);
  }, [query]);

  // Handle input change
  const handleInputChange = (event: React.SyntheticEvent, newInputValue: string) => {
    setInputValue(newInputValue);

    // Create a synthetic event to pass to the onChange handler
    const syntheticEvent = {
      target: {
        value: newInputValue
      }
    } as React.ChangeEvent<HTMLInputElement>;

    onChange(syntheticEvent);
  };

  // Handle option selection
  const handleOptionSelect = (
    _event: React.SyntheticEvent,
    value: string | ManagerOption | null,
    _reason: AutocompleteChangeReason
  ) => {
    if (typeof value === 'string') {
      // Handle free text input case
      setSelectedOption(null);
      if (onOptionSelect) {
        onOptionSelect(null);
      }
      return;
    }

    // Handle ManagerOption case
    setSelectedOption(value);

    if (onOptionSelect) {
      onOptionSelect(value);
    }

    if (value) {
      const syntheticEvent = {
        target: {
          value: `${value.firstName} ${value.lastName}`
        }
      } as React.ChangeEvent<HTMLInputElement>;

      onChange(syntheticEvent);
    }
  };

  return (
    <Autocomplete
      fullWidth
      freeSolo
      options={options}
      getOptionLabel={(option) => {
        if (typeof option === 'string') return option;
        return `${option.firstName} ${option.lastName}`;
      }}
      inputValue={inputValue}
      onInputChange={handleInputChange}
      onChange={handleOptionSelect}
      renderInput={(params) => (
        <TextField
          {...params}
          fullWidth
          placeholder={placeholder}
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '10px',
            },
          }}
        />
      )}
      renderOption={(props, option, { inputValue }) => {
        // Skip rendering if option is a string (from freeSolo)
        if (typeof option === 'string') {
          return null;
        }

        const fullName = `${option.firstName} ${option.lastName}`;
        const matches = match(fullName, inputValue);
        const parts = parse(fullName, matches);

        return (
          <ListItem {...props} key={option.id}>
            {option.avatarUrl && (
              <Avatar
                src={option.avatarUrl}
                alt={fullName}
                sx={{ mr: 2, width: 40, height: 40 }}
              />
            )}
            <ListItemText
              primary={
                <Box component="span">
                  {parts.map((part, index) => (
                    <Typography
                      key={index}
                      component="span"
                      sx={{ fontWeight: part.highlight ? 'bold' : 'regular' }}
                      color={part.highlight ? 'primary.main' : 'text.primary'}
                    >
                      {part.text}
                    </Typography>
                  ))}
                </Box>
              }
              secondary={option.email}
            />
            {option.role && (
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{
                  ml: 1,
                  bgcolor: 'background.neutral',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1
                }}
              >
                {option.role}
              </Typography>
            )}
          </ListItem>
        );
      }}
      sx={{
        ...sx,
      }}
    />
  );
}
