import { useState, useCallback, useEffect } from 'react';
import { useCategoriesApi, Category } from 'src/services/api/categories-api';
import { MOCK_CATEGORIES } from 'src/services/mock/categories-mock';
import { CategoryFormValues } from '../form/category-schema';

// This is a custom hook that combines the API services with local state management
export const useCategoriesView = () => {
  // State for categories
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  // Get the API hooks
  const { useGetCategories, useCreateCategory, useUpdateCategory, useDeleteCategory } =
    useCategoriesApi();

  // API hooks
  const { mutate: createCategory } = useCreateCategory((data) => {
    // Add the new category to the local state
    setCategories((prev) => [...prev, data]);
    handleCloseDialog();
  });

  const { mutate: updateCategory } = useUpdateCategory(selectedCategory?.id || '', () => {
    // Refresh the categories list
    fetchCategories();
    handleCloseDialog();
  });

  const { mutate: deleteCategory } = useDeleteCategory(() => {
    // Refresh the categories list
    fetchCategories();
  });

  // Get categories data from the API
  const { data: categoriesData, isLoading, isError, refetch } = useGetCategories();

  // Update local state when API data changes
  useEffect(() => {
    if (categoriesData) {
      setCategories(categoriesData);
      setLoading(false);
      setError(null);
    }
  }, [categoriesData]);

  // Update loading state
  useEffect(() => {
    setLoading(isLoading);
  }, [isLoading]);

  // Update error state
  useEffect(() => {
    if (isError) {
      setError('Failed to fetch categories');
    }
  }, [isError]);

  // Fetch categories function for manual refetching
  const fetchCategories = useCallback(() => {
    setLoading(true);
    refetch();
  }, [refetch]);

  // For development, use mock data if API call fails
  useEffect(() => {
    if (isError && import.meta.env.DEV) {
      console.log('Using mock data for categories');
      setCategories(MOCK_CATEGORIES);
      setLoading(false);
      setError(null);
    }
  }, [isError]);

  // Initial fetch
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  // Dialog handlers
  const handleOpenDialog = useCallback((category?: Category) => {
    setSelectedCategory(category || null);
    setOpenDialog(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
    setSelectedCategory(null);
  }, []);

  // Open the dialog to create a new category
  const handleOpenCreateDialog = useCallback(() => {
    setSelectedCategory(null);
    setOpenDialog(true);
  }, []);

  // Handle creating a new category
  const handleCreateCategory = useCallback(
    (data: CategoryFormValues) => {
      // Use the mutation from the API hook
      createCategory(data, {
        onError: (err) => {
          console.error('Failed to create category:', err);

          // For development, simulate success with mock data
          if (import.meta.env.DEV) {
            const newCategory = {
              id: String(categories.length + 1),
              name: data.name,
              description: data.description,
              icon: data.icon,
              colorType: data.colorType,
              agentsCount: 0,
            } as Category;

            setCategories((prev) => [...prev, newCategory]);
            handleCloseDialog();
          }
        },
      });
    },
    [categories, createCategory, handleCloseDialog]
  );

  // Handle updating a category
  const handleUpdateCategory = useCallback(
    (data: CategoryFormValues) => {
      if (!selectedCategory) return;

      // Use the mutation from the API hook
      updateCategory(data, {
        onError: (err) => {
          console.error('Failed to update category:', err);

          // For development, simulate success with mock data
          if (import.meta.env.DEV) {
            const updatedCategory = {
              ...selectedCategory,
              name: data.name,
              description: data.description,
              icon: data.icon,
              colorType: data.colorType,
            };

            setCategories((prev) =>
              prev.map((cat) => (cat.id === selectedCategory.id ? updatedCategory : cat))
            );
            handleCloseDialog();
          }
        },
      });
    },
    [selectedCategory, updateCategory, handleCloseDialog]
  );

  // Handle deleting a category
  const handleDeleteCategory = useCallback(
    (id: string) => {
      // Use the mutation from the API hook
      deleteCategory(id, {
        onError: (err) => {
          console.error('Failed to delete category:', err);

          // For development, simulate success with mock data
          if (import.meta.env.DEV) {
            setCategories((prev) => prev.filter((cat) => cat.id !== id));
          }
        },
      });
    },
    [deleteCategory]
  );

  // Handle editing a category
  const handleEditCategory = useCallback(
    (id: string) => {
      const category = categories.find((cat) => cat.id === id);
      if (category) {
        handleOpenDialog(category);
      }
    },
    [categories, handleOpenDialog]
  );

  return {
    // State
    categories,
    loading,
    error,
    openDialog,
    selectedCategory,

    // Handlers
    handleOpenDialog,
    handleCloseDialog,
    handleCreateCategory,
    handleUpdateCategory,
    handleDeleteCategory,
    handleEditCategory,
    handleOpenCreateDialog,

    // Refetch
    refetch: fetchCategories,
  };
};
