import { useState, useRef } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import {
  TextField,
  Box,
  Typography,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ClickAwayListener,
  InputAdornment,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';

interface Agent {
  id: string;
  name: string;
  avatar: string;
}

const MOCK_AGENTS: Agent[] = [
  {
    id: 'agent1',
    name: '<PERSON> hello world hello',
    avatar: '/assets/images/avatars/avatar_1.jpg',
  },
  {
    id: 'agent2',
    name: '<PERSON>',
    avatar: '/assets/images/avatars/avatar_2.jpg',
  },
  {
    id: 'agent3',
    name: '<PERSON>',
    avatar: '/assets/images/avatars/avatar_3.jpg',
  },
  {
    id: 'agent4',
    name: '<PERSON>',
    avatar: '/assets/images/avatars/avatar_4.jpg',
  },
  {
    id: 'agent5',
    name: '<PERSON>',
    avatar: '/assets/images/avatars/avatar_5.jpg',
  },
];
// ----------------------------------------------------------------------

interface InstructionFieldProps {
  name: string;
  label?: string;
  placeholder?: string;
  helperText?: React.ReactNode;
  multiline?: boolean;
  rows?: number;
  fullWidth?: boolean;
  required?: boolean;
  disabled?: boolean;
  sx?: object;
}

export function RHFInstructionField({
  name,
  label,
  placeholder,
  helperText,
  multiline = true,
  rows = 4,
  fullWidth = true,
  required = false,
  disabled = false,
  sx,
  ...other
}: InstructionFieldProps) {
  const { control } = useFormContext();
  const [mentionAnchorEl, setMentionAnchorEl] = useState<null | HTMLElement>(null);
  const [cursorPosition, setCursorPosition] = useState(0);
  const textFieldRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState(MOCK_AGENTS);
  const [selectedIndex, setSelectedIndex] = useState(0);

  // Function to format text with highlighted curly braces content
  const formatText = (text: string) => {
    if (!text) return '';

    // Use a regex that captures full names after @ symbol
    const parts = text.split(/({[^}]*})|(@[A-Za-z\s]+ )/g).filter(Boolean);

    return parts.map((part, index) => {
      if (part.startsWith('{') && part.endsWith('}')) {
        // Extract content within curly braces
        const content = part.substring(1, part.length - 1);
        return (
          <span
            key={index}
            style={{
              backgroundColor: '#FFC9B2', // Light orange background
              color: '#D84315', // Dark orange text
              padding: '2px 4px',
              borderRadius: '4px',
              fontWeight: 'medium',
            }}
          >
            {content}
          </span>
        );
      }

      if (part.startsWith('@')) {
        // Handle @ mentions - capture the full name
        const mentionText = part.trim();
        const spaceAfterMention = part.endsWith(' ') ? ' ' : '';

        // Find if this mention matches any agent's full name
        const matchedAgent = MOCK_AGENTS.find(
          (agent) => mentionText === `@${agent.name}` || mentionText === `@${agent.name} `
        );

        if (matchedAgent) {
          // This is a full agent name mention
          return (
            <span key={index}>
              <span
                style={{
                  backgroundColor: '#E3F2FD', // Light blue background
                  color: '#1976D2', // Blue text
                  padding: '2px 4px',
                  borderRadius: '4px',
                  fontWeight: 'medium',
                }}
              >
                {mentionText.trim()}
              </span>
              {spaceAfterMention}
            </span>
          );
        }

        // If not a full match, just return the text as is
        return part;
      }

      return part;
    });
  };

  // Handle text change and check for @ mentions
  const handleTextChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    onChange: (value: string) => void
  ) => {
    const value = event.target.value;
    onChange(value);

    // Get cursor position
    const cursorPos = event.target.selectionStart || 0;
    setCursorPosition(cursorPos);

    // Check if we're in a mention context
    const textBeforeCursor = value.substring(0, cursorPos);
    const mentionMatch = textBeforeCursor.match(/@([^\s@]*)$/);

    if (mentionMatch) {
      // We have a mention match
      setMentionAnchorEl(event.target);

      // Get the query text after @ symbol
      const query = mentionMatch[1] || '';
      setSearchQuery(query);

      // Filter agents based on the query
      const filtered = MOCK_AGENTS.filter((agent) =>
        agent.name.toLowerCase().includes(query.toLowerCase())
      );

      setFilteredAgents(filtered);
      setSelectedIndex(0); // Reset selection when query changes
    } else {
      setMentionAnchorEl(null);
      setSearchQuery('');
      setFilteredAgents(MOCK_AGENTS);
    }
  };

  // Handle selecting a mention
  const handleSelectMention = (agent: Agent, value: string, onChange: (value: string) => void) => {
    const textBeforeCursor = value.substring(0, cursorPosition);
    const mentionMatch = textBeforeCursor.match(/@([^\s@]*)$/);

    if (mentionMatch) {
      const startPos = textBeforeCursor.lastIndexOf('@');
      // Use the full agent name with @ symbol
      const newText =
        value.substring(0, startPos) + `@${agent.name} ` + value.substring(cursorPosition);

      onChange(newText);
    } else {
      // If no match, just append the agent name at the cursor position
      const newText =
        value.substring(0, cursorPosition) + `@${agent.name} ` + value.substring(cursorPosition);
      onChange(newText);
    }

    // Reset states
    setMentionAnchorEl(null);
    setSearchQuery('');
    setFilteredAgents(MOCK_AGENTS);
    setSelectedIndex(0);
  };

  // Close mention dropdown when clicking away
  const handleClickAway = () => {
    setMentionAnchorEl(null);
    setSearchQuery('');
    setFilteredAgents(MOCK_AGENTS);
    setSelectedIndex(0);
  };

  // Handle keyboard navigation in the dropdown
  const handleKeyDown = (
    event: React.KeyboardEvent<HTMLDivElement>,
    value: string,
    onChange: (value: string) => void
  ) => {
    // Only process if the mention dropdown is open
    if (mentionAnchorEl) {
      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setSelectedIndex((prevIndex) =>
            prevIndex < filteredAgents.length - 1 ? prevIndex + 1 : prevIndex
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setSelectedIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : 0));
          break;
        case 'Enter':
          event.preventDefault();
          if (filteredAgents.length > 0) {
            handleSelectMention(filteredAgents[selectedIndex], value, onChange);
          }
          break;
        case 'Escape':
          event.preventDefault();
          setMentionAnchorEl(null);
          setSearchQuery('');
          setFilteredAgents(MOCK_AGENTS);
          setSelectedIndex(0);
          break;
        default:
          break;
      }
    }
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <ClickAwayListener onClickAway={handleClickAway}>
          <Box sx={{ position: 'relative', width: '100%' }}>
            <Typography variant="subtitle2" gutterBottom>
              {label}
            </Typography>

            {/* Single field with formatted text */}
            <Box
              sx={{
                border: '1px solid',
                borderColor: error ? 'error.main' : isFocused ? 'primary.main' : 'divider',
                borderRadius: 1,
                position: 'relative',
                minHeight: rows * 24,
                p: 0,
                '&:hover': {
                  borderColor: 'text.primary',
                },
                '&:focus-within': {
                  borderColor: 'primary.main',
                  boxShadow: '0 0 0 2px rgba(0, 171, 85, 0.2)',
                },
              }}
            >
              {/* Visible textarea with formatted text overlay */}
              <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
                <TextField
                  {...field}
                  ref={textFieldRef}
                  fullWidth
                  placeholder={placeholder}
                  error={!!error}
                  multiline
                  rows={rows}
                  disabled={disabled}
                  onChange={(e) => handleTextChange(e, field.onChange)}
                  onKeyDown={(e) => handleKeyDown(e, field.value, field.onChange)}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  sx={{
                    '& .MuiInputBase-root': {
                      padding: 0,
                      height: '100%',
                    },
                    '& .MuiInputBase-input': {
                      padding: '16px',
                      color: 'rgba(0,0,0,0.05)', // Very slight color to see text being typed
                      caretColor: '#00AB55', // Primary green color for cursor
                      textShadow: '0 0 0 transparent', // Remove any text shadow
                      position: 'relative',
                      zIndex: 2, // Higher z-index to ensure cursor is on top
                      fontSize: '1rem', // Match font size with the overlay text
                      lineHeight: 1.8, // Match line height with the overlay text
                      fontFamily: 'inherit',
                    },
                  }}
                  {...other}
                />

                {/* Formatted display overlay */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    p: '16px', // Match exactly with the input padding
                    pointerEvents: 'none', // Allow clicks to pass through to the input
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word',
                    color: field.value ? 'text.primary' : 'text.secondary',
                    zIndex: 1, // Lower than the cursor but higher than the base
                    fontSize: '1rem', // Match with input
                    lineHeight: 1.8,
                    fontFamily: 'inherit',
                  }}
                >
                  {field.value ? (
                    <Box sx={{ lineHeight: 1.8 }}>{formatText(field.value)}</Box>
                  ) : (
                    <Box sx={{ color: 'text.secondary' }}>{placeholder}</Box>
                  )}
                </Box>
              </Box>

              {/* Agent menu popup when typing @ */}
              {Boolean(mentionAnchorEl) && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%', // Center vertically
                    left: '50%', // Center horizontally
                    transform: 'translate(-50%, -50%)', // Perfect centering
                    width: 280,
                    maxHeight: 350,
                    bgcolor: 'background.paper',
                    boxShadow: 6,
                    borderRadius: 1,
                    overflow: 'hidden', // Hide overflow except for the list
                    zIndex: 20, // Higher z-index to be on top of everything
                    border: '1px solid',
                    borderColor: 'primary.main',
                    display: 'flex',
                    flexDirection: 'column',
                  }}
                >
                  {/* Search field at the top */}
                  <Box sx={{ p: 1, borderBottom: '1px solid', borderColor: 'divider' }}>
                    <TextField
                      fullWidth
                      size="small"
                      placeholder="Search names..."
                      value={searchQuery}
                      onChange={(e) => {
                        const query = e.target.value;
                        setSearchQuery(query);
                        const filtered = MOCK_AGENTS.filter((agent) =>
                          agent.name.toLowerCase().includes(query.toLowerCase())
                        );
                        setFilteredAgents(filtered);
                        setSelectedIndex(0);
                      }}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="eva:search-fill" width={20} height={20} />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          '& fieldset': {
                            borderColor: 'divider',
                          },
                          '&:hover fieldset': {
                            borderColor: 'primary.main',
                          },
                        },
                      }}
                    />
                  </Box>

                  {/* List of agents */}
                  <Box sx={{ overflow: 'auto', maxHeight: 280 }}>
                    <List dense sx={{ p: 0 }}>
                      {filteredAgents.length > 0 ? (
                        filteredAgents.map((agent, index) => (
                          <ListItem
                            key={agent.id}
                            onClick={() => handleSelectMention(agent, field.value, field.onChange)}
                            sx={{
                              py: 0.75,
                              cursor: 'pointer',
                              bgcolor: index === selectedIndex ? 'primary.lighter' : 'transparent',
                              '&:hover': {
                                bgcolor:
                                  index === selectedIndex ? 'primary.lighter' : 'action.hover',
                              },
                            }}
                          >
                            <ListItemAvatar sx={{ minWidth: 36 }}>
                              <Avatar
                                src={agent.avatar}
                                alt={agent.name}
                                sx={{ width: 28, height: 28 }}
                              />
                            </ListItemAvatar>
                            <ListItemText
                              primary={agent.name}
                              primaryTypographyProps={{
                                variant: 'body2',
                                fontWeight: index === selectedIndex ? 'bold' : 'medium',
                              }}
                            />
                          </ListItem>
                        ))
                      ) : (
                        <ListItem sx={{ py: 2, justifyContent: 'center' }}>
                          <Typography variant="body2" color="text.secondary">
                            No matching names found
                          </Typography>
                        </ListItem>
                      )}
                    </List>
                  </Box>

                  {/* Helper text */}
                  <Box
                    sx={{
                      p: 1,
                      borderTop: '1px solid',
                      borderColor: 'divider',
                      bgcolor: 'background.neutral',
                    }}
                  >
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{ display: 'block', textAlign: 'center' }}
                    >
                      Press Enter to select or click on a name
                    </Typography>
                  </Box>
                </Box>
              )}
            </Box>

            {/* Error message */}
            {error && (
              <Typography color="error" variant="caption" sx={{ mt: 0.5, display: 'block' }}>
                {error.message}
              </Typography>
            )}
            {helperText && !error && (
              <Typography
                variant="caption"
                sx={{ mt: 0.5, display: 'block', color: 'text.secondary' }}
              >
                {helperText}
              </Typography>
            )}
          </Box>
        </ClickAwayListener>
      )}
    />
  );
}
