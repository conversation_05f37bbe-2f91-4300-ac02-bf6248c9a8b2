import { Card } from '@mui/material';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { paths } from 'src/routes/paths';
import MyProfileTab from '../tabs/my-profile-tab';

// ----------------------------------------------------------------------

export function ProfileView() {
  return (
    <>
      <CustomBreadcrumbs
        heading="Profile"
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'Profile', href: paths.dashboard.profile.root },
          { name: 'My Profile' },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card sx={{ p: 3 }}>
        <MyProfileTab />
      </Card>
    </>
  );
}
