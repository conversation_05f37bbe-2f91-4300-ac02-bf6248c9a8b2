import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import ViewAllCategories from 'src/sections/categories/view/categories-view';

const CategoriesPage = () => {
  const { t } = useTranslation();

  return (
    <>
      <Helmet>
        <title>{`${t('pages.dashboard.title')}: ${t('pages.dashboard.categories')}`}</title>
      </Helmet>
      <ViewAllCategories />
    </>
  );
};

export default CategoriesPage;
