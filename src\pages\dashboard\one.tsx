import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';

import { CONFIG } from 'src/config-global';

import { BlankView } from 'src/sections/blank/view';

// ----------------------------------------------------------------------

export default function Page() {
  const { t } = useTranslation();
  const title = `${t('pages.dashboard.pageOne')} | ${t('pages.dashboard.title')} - ${CONFIG.site.name}`;

  return (
    <>
      <Helmet>
        <title>{title}</title>
      </Helmet>

      <BlankView title={t('pages.dashboard.pageOne')} />
    </>
  );
}
