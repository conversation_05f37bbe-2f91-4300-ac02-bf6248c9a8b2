import { Container, Stack, Typography, Box, Chip, IconButton } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppTable } from 'src/components/table/app-table/app-table';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
import { AppButton } from 'src/components/common';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';
import { useManagersView, ManagerData, STATUS_TAB_OPTIONS } from './use-managers-view';
import { ManagerSearchBar, ManagerOption } from '../components';

// ----------------------------------------------------------------------

// Mock data for managers
const MOCK_MANAGERS: ManagerData[] = [
  {
    id: '1',
    firstName: '<PERSON>',
    lastName: 'Doe',
    username: 'johndo<PERSON>',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_1.jpg',
    status: 'active',
    role: 'Admin',
  },
  {
    id: '2',
    firstName: 'Jane',
    lastName: 'Smith',
    username: 'janesmith',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_2.jpg',
    status: 'active',
    role: 'Manager',
  },
  {
    id: '3',
    firstName: 'Michael',
    lastName: 'Johnson',
    username: 'michaelj',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_3.jpg',
    status: 'pending',
    role: 'Manager',
  },
  {
    id: '4',
    firstName: 'Emily',
    lastName: 'Williams',
    username: 'emilyw',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_4.jpg',
    status: 'banned',
    role: 'Manager',
  },
  {
    id: '5',
    firstName: 'David',
    lastName: 'Brown',
    username: 'davidb',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_5.jpg',
    status: 'rejected',
    role: 'Manager',
  },
  {
    id: '6',
    firstName: 'Sarah',
    lastName: 'Miller',
    username: 'sarahm',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_6.jpg',
    status: 'active',
    role: 'Manager',
  },
  {
    id: '7',
    firstName: 'Robert',
    lastName: 'Wilson',
    username: 'robertw',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_7.jpg',
    status: 'pending',
    role: 'Manager',
  },
];

export const ManagersView = () => {
  const router = useRouter();

  // Use the custom hook to manage managers
  const {
    filteredManagers,
    openConfirmDialog,
    openLoginConfirmDialog,
    selectedIds,
    selectedId,
    selectedManagerForLogin,
    table,
    columns,
    statusFilter,
    searchQuery,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleOpenLoginConfirmDialog,
    handleCloseLoginConfirmDialog,
    handleDeleteManagers,
    handleDeleteSingleManager,
    handleSelectRow,
    handleSelectAllRows,
    handleStatusFilterChange,
    handleSearchChange,
    executeLoginAsManager,
  } = useManagersView(MOCK_MANAGERS);

  // Handle adding a new manager
  const handleAddManager = () => {
    router.push(paths.dashboard.managers.create);
  };

  // Handle status tab change
  const handleStatusTabChange = (_: React.SyntheticEvent, value: string) => {
    handleStatusFilterChange(value);
  };

  // Handle manager selection from autocomplete
  const handleManagerSelect = (manager: ManagerOption | null) => {
    if (manager) {
      // You can add additional logic here if needed
      console.log(`Selected manager: ${manager.firstName} ${manager.lastName}`);
    }
  };

  return (
    <Container maxWidth="xl">
      <CustomBreadcrumbs
        heading="Managers"
        links={[{ name: 'Dashboard', href: paths.dashboard.root }, { name: 'Managers' }]}
        action={
          <AppButton
            label="Create Manager"
            variant="outlined"
            color="primary"
            startIcon={<Iconify icon="eva:plus-fill" />}
            onClick={handleAddManager}
          />
        }
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      {/* Filters and Search Section */}
      <Box sx={{ mb: 3 }}>
        {/* Status Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Stack direction="row" spacing={2}>
            {STATUS_TAB_OPTIONS.map((tab) => {
              // Count managers by status
              const count =
                tab.value === 'all'
                  ? filteredManagers.length
                  : MOCK_MANAGERS.filter((manager) => manager.status === tab.value).length;

              return (
                <Box
                  key={tab.value}
                  onClick={(e) => handleStatusTabChange(e, tab.value)}
                  sx={{
                    py: 2,
                    px: 3,
                    cursor: 'pointer',
                    fontWeight: statusFilter === tab.value ? 'bold' : 'normal',
                    borderBottom: statusFilter === tab.value ? '2px solid' : 'none',
                    borderColor: 'main.default',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  {tab.label}
                  <Chip
                    label={count}
                    size="small"
                    variant={statusFilter === tab.value ? 'filled' : 'soft'}
                    color={
                      tab.value === 'all'
                        ? 'default'
                        : tab.value === 'active'
                          ? 'success'
                          : tab.value === 'pending'
                            ? 'warning'
                            : tab.value === 'banned'
                              ? 'error'
                              : 'default'
                    }
                    sx={{ minWidth: 30 }}
                  />
                </Box>
              );
            })}
          </Stack>
        </Box>

        {/* Search Field */}
        <Box sx={{ mb: 3 }}>
          <ManagerSearchBar
            query={searchQuery}
            onChange={handleSearchChange}
            placeholder="Search by name or email..."
            options={filteredManagers}
            onOptionSelect={handleManagerSelect}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '48px',
                border: '1px solid rgba(0, 0, 0, 0.24)',
              },
            }}
          />
        </Box>

        {/* Results Section */}
        <Box sx={{ mb: 2 }}>


<Typography variant="body2" color="text.secondary">
  <Typography component="span" variant="subtitle2" color="initial" fontWeight="bold">
    {filteredManagers.length}
  </Typography> Results found
</Typography>
        </Box>

        {/* Status Section */}
        <Box sx={{ mb: 3 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
              Status :
            {statusFilter !== 'all' && (
              <Chip

                label={STATUS_TAB_OPTIONS.find(s => s.value === statusFilter)?.label}
                size="small"
                color={
                  statusFilter === 'active'
                    ? 'success'
                    : statusFilter === 'pending'
                      ? 'warning'
                      : statusFilter === 'banned'
                        ? 'error'
                        : statusFilter === 'rejected'
                          ? 'info'
                          : 'default'
                }
                onDelete={() => handleStatusFilterChange('all')}
                sx={{  mx:1}}
              />
            )}
            {statusFilter !== 'all' && (
              <IconButton
                size="small"
                onClick={() => handleStatusFilterChange('all')}
                sx={{ color: 'error.main' }}
                title="Clear all filters"
              >
                <Iconify icon="eva:trash-2-outline" width={20} height={20} /> clear
              </IconButton>
            )}
            {statusFilter === 'all' && (
              <Typography variant="body2" color="text.secondary" sx={{ py: 1 }}>
                No filters applied
              </Typography>
            )}
            </Typography>

          </Stack>

        </Box>
      </Box>

      <AppTable<ManagerData>
        headLabels={[
          { id: 'manager', label: 'Manager' },
          { id: 'role', label: 'Role' },
          { id: 'status', label: 'Status' },
          { id: 'actions', label: '' },
        ]}
        dataCount={filteredManagers.length}
        data={filteredManagers}
        columns={columns}
        table={table}
        select={{
          handleSelectRow,
          handleSelectAllRows,
          idPath: 'id',
          rowCount: filteredManagers.length,
          numSelected: table.selected.length,
          selectedRowsActions: [
            {
              type: 'button',
              label: 'Delete Selected',
              handler: handleOpenConfirmDialog,
              color: 'error',
              isLoading: false,
            },
          ],
        }}
        noDataLabel="No managers found"
        sx={{
          '& .MuiTablePagination-root': {
            position: 'relative',
          },
        }}
      />

      {/* Actions Menu is now handled by LongMenu component */}

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        open={openConfirmDialog}
        onClose={handleCloseConfirmDialog}
        close={handleCloseConfirmDialog}
        title={
          <Typography variant="h3" textAlign="center">
            Delete {selectedId ? 'Manager' : 'Managers'}?
          </Typography>
        }
        content={
          <Typography variant="body1">
            {selectedId
              ? 'Are you sure you want to delete this manager?'
              : `Are you sure you want to delete ${selectedIds.length} selected ${selectedIds.length === 1 ? 'manager' : 'managers'}?`}
          </Typography>
        }
        icon={
          <Box sx={{ textAlign: 'center' }}>
            <Iconify
              icon="eva:alert-triangle-fill"
              sx={{ width: 64, height: 64, color: 'error.main' }}
            />
          </Box>
        }
        action={
          <Stack direction="row" justifyContent="center" spacing={1} sx={{ width: '100%' }}>
            <AppButton
              sx={{ width: '45%' }}
              label="Delete"
              variant="contained"
              color="error"
              onClick={selectedId ? handleDeleteSingleManager : handleDeleteManagers}
            />
            <AppButton
              sx={{ width: '45%' }}
              label="Cancel"
              variant="outlined"
              color="inherit"
              onClick={handleCloseConfirmDialog}
            />
          </Stack>
        }
      />

      {/* Confirm Login As Dialog */}
      <ConfirmDialog
        open={openLoginConfirmDialog}
        onClose={handleCloseLoginConfirmDialog}
        close={handleCloseLoginConfirmDialog}
        title={
          <Typography variant="h3" textAlign="center">
            Login as {selectedManagerForLogin ? `${selectedManagerForLogin.firstName} ${selectedManagerForLogin.lastName}` : 'Manager'}?
          </Typography>
        }
        content={
          <Typography variant="body1">
You’re one step away from signing in as another user?          </Typography>
        }
        icon={
          <Box sx={{ textAlign: 'center' }}>
            <Iconify
              icon="eva:alert-triangle-fill"
              sx={{ width: 64, height: 64, color: 'warning.main' }}
            />
          </Box>
        }
        action={
          <Stack direction="row" justifyContent="center" spacing={1} sx={{ width: '100%' }}>
            <AppButton
              sx={{ width: '45%' }}
              label="Login"
              variant="contained"
              color="primary"
              onClick={executeLoginAsManager}
            />
            <AppButton
              sx={{ width: '45%' }}
              label="Cancel"
              variant="outlined"
              color="inherit"
              onClick={handleCloseLoginConfirmDialog}
            />
          </Stack>
        }
      />
    </Container>
  );
};
