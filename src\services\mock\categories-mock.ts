import { Category } from '../api/categories-api';

// Mock data for categories
export const MOCK_CATEGORIES: Category[] = [
  {
    id: '0',
    name: 'Custom',
    description: 'Custom category with a custom color',
    agentsCount: 2,
    icon: 'mdi:palette',
    colorType: 'custom',
    customColor: '#FF5733',
  },
  {
    id: '1',
    name: 'Sales',
    description: 'Sales team agents handling customer inquiries and sales processes',
    agentsCount: 12,
    icon: 'mdi:account-tie',
    colorType: 'primary',
  },
  {
    id: '2',
    name: 'Support',
    description: 'Technical support team providing assistance to customers',
    agentsCount: 8,
    icon: 'mdi:headset',
    colorType: 'success',
  },
  {
    id: '3',
    name: 'Marketing',
    description: 'Marketing team handling campaigns and promotions',
    agentsCount: 5,
    icon: 'mdi:bullhorn',
    colorType: 'warning',
  },
  {
    id: '4',
    name: 'Development',
    description: 'Software development team building new features',
    agentsCount: 15,
    icon: 'mdi:code-braces',
    colorType: 'info',
  },
  {
    id: '5',
    name: 'HR',
    description: 'Human resources team managing employee relations',
    agentsCount: 3,
    icon: 'mdi:account-group',
    colorType: 'error',
  },
];

// Mock API handlers
export const mockCategoriesApi = {
  // Get all categories
  getCategories: () => {
    return Promise.resolve({ data: MOCK_CATEGORIES });
  },

  // Get a single category by ID
  getCategory: (id: string) => {
    const category = MOCK_CATEGORIES.find((cat) => cat.id === id);
    if (!category) {
      return Promise.reject(new Error('Category not found'));
    }
    return Promise.resolve({ data: category });
  },

  // Create a new category
  createCategory: (categoryData: Omit<Category, 'id' | 'agentsCount'>) => {
    const newCategory: Category = {
      id: String(MOCK_CATEGORIES.length + 1),
      ...categoryData,
      agentsCount: 0,
    };
    return Promise.resolve({ data: newCategory });
  },

  // Update a category
  updateCategory: (id: string, categoryData: Partial<Omit<Category, 'id'>>) => {
    const categoryIndex = MOCK_CATEGORIES.findIndex((cat) => cat.id === id);
    if (categoryIndex === -1) {
      return Promise.reject(new Error('Category not found'));
    }
    const updatedCategory = {
      ...MOCK_CATEGORIES[categoryIndex],
      ...categoryData,
    };
    return Promise.resolve({ data: updatedCategory });
  },

  // Delete a category
  deleteCategory: (id: string) => {
    const categoryIndex = MOCK_CATEGORIES.findIndex((cat) => cat.id === id);
    if (categoryIndex === -1) {
      return Promise.reject(new Error('Category not found'));
    }
    return Promise.resolve({ data: { success: true } });
  },
};
