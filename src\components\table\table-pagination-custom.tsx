// @mui
import Box from '@mui/material/Box';
import FormControlLabel from '@mui/material/FormControlLabel';
import Switch from '@mui/material/Switch';
import TablePagination, { TablePaginationProps } from '@mui/material/TablePagination';
import { SxProps, Theme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';

// ----------------------------------------------------------------------

type Props = {
  dense?: boolean;
  onChangeDense?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  sx?: SxProps<Theme>;
  rowsPerPageOptions?: number[];
};

export default function TablePaginationCustom({
  dense,
  onChangeDense,
  rowsPerPageOptions = [10, 25, 100],
  sx,
  ...other
}: Props & TablePaginationProps) {
  const { t } = useTranslation();
  return (
    <Box sx={{ position: 'relative', ...sx }}>
      <TablePagination
        rowsPerPageOptions={rowsPerPageOptions}
        labelRowsPerPage={t('tables.rowsPerPageLabel')}
        component="div"
        {...other}
        page={other.page - 1}
        sx={{
          borderTopColor: 'transparent',
        }}
      />

      {onChangeDense && (
        <FormControlLabel
          label={t('components.tables.dense')}
          control={<Switch checked={dense} onChange={onChangeDense} />}
          sx={{
            pl: 2,
            py: 1.5,
            top: 0,
            left: 0,
            position: 'absolute',
          }}
        />
      )}
    </Box>
  );
}
