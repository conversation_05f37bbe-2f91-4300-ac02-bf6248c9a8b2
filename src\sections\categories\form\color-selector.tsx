import {
  Box,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormHelperText,
  Stack,
  Typography,
  useTheme,
  TextField,
  InputAdornment,
  IconButton,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { Controller, Control } from 'react-hook-form';
import { CategoryFormValues } from './category-schema';

interface ColorOption {
  value: string;
  label: string;
  color: string;
  icon?: string;
}

interface ColorSelectorProps {
  control: Control<CategoryFormValues>;
  error?: boolean;
  helperText?: string;
}

export default function ColorSelector({ control, error, helperText }: ColorSelectorProps) {
  const theme = useTheme();
  // Access form context if needed in the future

  const colorOptions: ColorOption[] = [
    { value: 'primary', label: 'Primary', color: theme.palette.primary.main },
    { value: 'secondary', label: 'Secondary', color: theme.palette.secondary.main },
    { value: 'success', label: 'Success', color: theme.palette.success.main },
    { value: 'warning', label: 'Warning', color: theme.palette.warning.main },
    { value: 'info', label: 'Info', color: theme.palette.info.main },
    { value: 'error', label: 'Error', color: theme.palette.error.main },
    { value: 'custom', label: 'Custom', color: '#FF5733', icon: 'eva:plus-fill' },
  ];

  return (
    <Stack spacing={1.5}>
      <Typography variant="subtitle2">Color Theme</Typography>

      <Controller
        name="colorType"
        control={control}
        render={({ field }) => (
          <FormControl component="fieldset" error={error} sx={{ marginLeft: '10px' }}>
            <RadioGroup row {...field} onChange={(e) => field.onChange(e.target.value)}>
              {colorOptions.map((option) => (
                <FormControlLabel
                  key={option.value}
                  value={option.value}
                  control={
                    <Radio
                      sx={{
                        '&.Mui-checked': {
                          color: option.color,
                        },
                      }}
                      icon={
                        option.icon ? (
                          <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 54,
                            height: 54,
                            bgcolor:'rgba(255, 226, 216, 1)',
                            color:'black',
                            borderRadius: '50%',
                            boxShadow: `0 0 0 1px rgba(255, 226, 216, 1), 0 0 0 1px rgba(255, 226, 216, 1)`,
                          }}
                        >
                          <Iconify icon={option.icon} width={25} height={25} />
                        </Box>
                        ) : (
                          <Box
                            sx={{
                              width: 54,
                              height: 54,
                              borderRadius: '50%',
                              bgcolor: option.color,
                              opacity: 0.6,
                            }}
                          />
                        )
                      }
                      checkedIcon={
                        option.icon ? (
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              width: 54,
                              height: 54,
                              bgcolor:'rgba(255, 226, 216, 1)',
                              color:'black',
                              borderRadius: '50%',
                              boxShadow: `0 0 0 1px rgba(255, 226, 216, 1), 0 0 0 1px rgba(255, 226, 216, 1)`,
                            }}
                          >
                            <Iconify icon={option.icon} width={25} height={25} />
                          </Box>
                        ) : (
                          <Box
                            sx={{
                              width: 54,
                              height: 54,
                              borderRadius: '50%',
                              bgcolor: option.color,
                              boxShadow: `0 0 0 2px ${theme.palette.background.paper}, 0 0 0 4px ${option.color}`,
                            }}
                          />
                        )
                      }
                    />
                  }
                  label={null}
                />
              ))}
            </RadioGroup>
            {helperText && <FormHelperText>{helperText}</FormHelperText>}
          </FormControl>
        )}
      />

      {/* Custom Color Input */}
      <Controller
        name="colorType"
        control={control}
        render={({ field: colorTypeField }) => (
          <Controller
            name="customColor"
            control={control}
            render={({ field, fieldState: { error: customColorError } }) => (
              <Box sx={{ mt: 2, display: colorTypeField.value === 'custom' ? 'block' : 'none' }}>
                <TextField
                  {...field}
                  fullWidth
                  label="Custom Color (Hex)"
                  placeholder="#FF5733"
                  error={!!customColorError}
                  helperText={customColorError?.message}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '50%',
                            bgcolor: field.value || '#FF5733',
                            mr: 1,
                          }}
                        />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          sx={{ p: 0.5 }}
                          edge="end"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <Box
                            component="input"
                            type="color"
                            value={field.value || '#FF5733'}
                            onChange={(e) => field.onChange(e.target.value)}
                            sx={{
                              width: '100%',
                              height: '100%',
                              border: 'none',
                              padding: 0,
                              cursor: 'pointer',
                              opacity: 0,
                              position: 'absolute',
                            }}
                          />
                          <Iconify icon="eva:color-palette-fill" width={24} height={24} />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{ display: 'block' }}
                />
              </Box>
            )}
          />
        )}
      />
    </Stack>
  );
}
