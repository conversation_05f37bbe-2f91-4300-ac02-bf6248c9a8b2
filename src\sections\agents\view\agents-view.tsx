import { Stack, Typography, Box, Grid, Chip } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { AppTable } from 'src/components/table/app-table/app-table';
import { paths } from 'src/routes/paths';
import { A<PERSON><PERSON><PERSON><PERSON>, AppContainer } from 'src/components/common';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';
import AgentForm from '../form/agent-form';
import { STATUS_TAB_OPTIONS, useAgentsView } from './use-agent-view';
// import AgentSearchBar, { AgentOption } from '../components/agent-search-bar';
import CategoryFilter from '../components/category-filter';
import AgentSearchBar, { AgentOption } from '../components/agent-search-bar';

// ----------------------------------------------------------------------

// Mock data for agents
const MOCK_AGENTS = [
  {
    id: '1',
    name: '<PERSON>',
    description: 'Senior Sales Representative',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_1.jpg',
    category: 'Sales',
    status: 'active',
  },
  {
    id: '2',
    name: 'Jane Smith',
    description: 'Customer Support Specialist',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_2.jpg',
    category: 'Support',
    status: 'active',
  },
  {
    id: '3',
    name: 'Michael Johnson',
    description: 'Marketing Strategist',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_3.jpg',
    category: 'Marketing',
    status: 'inactive',
  },
  {
    id: '4',
    name: 'Emily Williams',
    description: 'HR Coordinator',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_4.jpg',
    category: 'HR',
    status: 'active',
  },
  {
    id: '5',
    name: 'David Brown',
    description: 'IT Support Specialist',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_5.jpg',
    category: 'IT',
    status: 'active',
  },
  {
    id: '6',
    name: 'Sarah Miller',
    description: 'UX Designer',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_6.jpg',
    category: 'UX',
    status: 'active',
  },
  {
    id: '7',
    name: 'James Wilson',
    description: 'Social Media Manager',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_7.jpg',
    category: 'Social Media',
    status: 'inactive',
  },
  {
    id: '8',
    name: 'Jessica Taylor',
    description: 'Marketing Coordinator',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_8.jpg',
    category: 'Marketing',
    status: 'active',
  },
  {
    id: '9',
    name: 'Robert Martinez',
    description: 'Sales Associate',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_9.jpg',
    category: 'Sales',
    status: 'inactive',
  },
];

// Agent data type
interface AgentData {
  id: string;
  name: string;
  description: string;
  email: string;
  avatarUrl: string;
  category: string;
  status: string;
}

export const AgentsView = () => {
  // Use the custom hook to manage agents
  const {
    filteredAgents,
    selectedAgent,
    openConfirmDialog,
    selectedIds,
    selectedId,
    formDialog,
    table,
    columns,
    statusTab,
    categoryFilter,
    searchQuery,
    categoryOptions,
    handleAddAgent,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleDeleteAgents,
    handleDeleteSingleAgent,
    handleSelectRow,
    handleSelectAllRows,
    handleFormSubmit,
    handleStatusTabChange,
    handleCategoryChange,
    handleSearchChange,
  } = useAgentsView(MOCK_AGENTS);

  return (
    <AppContainer
      title="Agents"
      routeLinks={[{ name: 'Dashboard', href: paths.dashboard.root }, { name: 'Agents' }]}
      buttons={[
        {
          label: 'Create New Agent',
          variant: 'outlined',
          startIcon: <Iconify icon="eva:plus-fill" />,
          onClick: handleAddAgent,
        },
      ]}
    >
      {/* Filters and Search Section */}
      <Box sx={{ mb: 3 }}>
        {/* Status Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Stack direction="row" spacing={2}>
            {STATUS_TAB_OPTIONS.map((tab) => {
              // Count agents by status
              const count =
                tab.value === 'all'
                  ? filteredAgents.length
                  : filteredAgents.filter((agent) => agent.status === tab.value).length;

              return (
                <Box
                  key={tab.value}
                  onClick={(e) => handleStatusTabChange(e, tab.value)}
                  sx={{
                    py: 2,
                    px: 3,
                    cursor: 'pointer',
                    // color: statusTab === tab.value ? 'primary.main' : 'text.secondary',
                    fontWeight: statusTab === tab.value ? 'bold' : 'normal',
                    borderBottom: statusTab === tab.value ? '2px solid' : 'none',
                    borderColor: 'global.inherit',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                >
                  {tab.label}
                  <Chip
                    label={count}
                    size="small"
                    variant={tab.value === 'all' ? 'filled' : 'soft'}
                    color={
                      tab.value === 'all'
                        ? 'default'
                        : tab.value === 'active'
                          ? 'success'
                          : 'primary'
                    }
                    sx={{ minWidth: 30 }}
                  />
                </Box>
              );
            })}
          </Stack>
        </Box>

        {/* Search and Category Filter */}
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <CategoryFilter
              value={categoryFilter}
              onChange={handleCategoryChange}
              options={categoryOptions}
            />
          </Grid>
          <Grid item xs={12} md={8}>
            <AgentSearchBar
              query={searchQuery}
              onChange={handleSearchChange}
              placeholder="Search by name, description, or email..."
              options={filteredAgents.map((agent: AgentData) => ({
                id: agent.id,
                name: agent.name,
                description: agent.description,
                email: agent.email,
                avatarUrl: agent.avatarUrl,
                category: agent.category,
                status: agent.status
              }))}
              onOptionSelect={(option: AgentOption | null) => {
                if (option) {
                  // You can add additional actions when an option is selected
                  // For example, you might want to view the agent details
                  console.log(`Selected agent: ${option.name}`);
                }
              }}
            />
          </Grid>
        </Grid>
      </Box>

      {/* Agents Table */}
      <AppTable<AgentData>
        headLabels={[
          { id: 'agent', label: 'Agent' },
          { id: 'category', label: 'Category' },
          { id: 'actions', label: '' },
        ]}
        dataCount={filteredAgents.length}
        data={filteredAgents}
        columns={columns}
        table={table}
        select={{
          handleSelectRow,
          handleSelectAllRows,
          idPath: 'id',
          rowCount: filteredAgents.length,
          numSelected: table.selected.length,
          selectedRowsActions: [
            {
              type: 'button',
              label: 'Delete Selected',
              handler: handleOpenConfirmDialog,
              color: 'error',
              isLoading: false,
            },
          ],
        }}
        noDataLabel="No agents found"
      />

      {/* Agent Form Dialog */}
      {formDialog.value && (
        <AgentForm
          open={formDialog.value}
          onClose={formDialog.onFalse}
          agent={selectedAgent}
          onSubmit={handleFormSubmit}
        />
      )}

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        open={openConfirmDialog}
        onClose={handleCloseConfirmDialog}
        close={handleCloseConfirmDialog}
        title={
          <Typography variant="h3" textAlign="center">
            Delete {selectedId ? 'Agent' : 'Agents'}?
          </Typography>
        }
        content={
          <Typography variant="body1">
            {selectedId
              ? 'Are you sure you want to delete this agent?'
              : `Are you sure you want to delete ${selectedIds.length} selected ${selectedIds.length === 1 ? 'agent' : 'agents'}?`}
          </Typography>
        }
        icon={
          <Box sx={{ textAlign: 'center' }}>
            <Iconify
              icon="eva:alert-triangle-fill"
              sx={{ width: 64, height: 64, color: 'error.main' }}
            />
          </Box>
        }
        action={
          <Stack direction="row" justifyContent="space-between"  spacing={1} sx={{ width: '100%' }}>
            <AppButton
              sx={{ width: '45%' }}
              label="Delete"
              variant="contained"
              color="error"
              onClick={selectedId ? handleDeleteSingleAgent : handleDeleteAgents}
            />
            <AppButton
              sx={{ width: '45%' }}
              label="Cancel"
              variant="outlined"
              color="inherit"
              onClick={handleCloseConfirmDialog}
            />
          </Stack>
        }
      />
    </AppContainer>
  );
};
