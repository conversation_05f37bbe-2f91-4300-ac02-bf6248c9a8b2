import { z as zod } from 'zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import InputAdornment from '@mui/material/InputAdornment';

import { AppButton } from 'src/components/common';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
import { RouterLink } from 'src/routes/components';

import { useBoolean } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

import { signUp } from 'src/auth/context/jwt';
import { useAuthContext } from 'src/auth/hooks';
import { CONFIG } from 'src/config-global';
import { Avatar } from '@mui/material';

// ----------------------------------------------------------------------

export const SignUpSchema = zod.object({
  name: zod.string().min(1, { message: 'Name is required!' }),
  email: zod
    .string()
    .min(1, { message: 'Email is required!' })
    .email({ message: 'Email must be a valid email address!' }),
  password: zod
    .string()
    .min(1, { message: 'Password is required!' })
    .min(6, { message: 'Password must be at least 6 characters!' }),
});

export type SignUpSchemaType = zod.infer<typeof SignUpSchema>;

// ----------------------------------------------------------------------

export function JwtSignUpView() {
  const { checkUserSession } = useAuthContext();
  const router = useRouter();
  const password = useBoolean();
  const [errorMsg, setErrorMsg] = useState('');
  const { t } = useTranslation();

  const methods = useForm<SignUpSchemaType>({
    resolver: zodResolver(SignUpSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
    },
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      await signUp({
        email: data.email,
        password: data.password,
        firstName: data.name,
        lastName: '',
      });
      await checkUserSession?.();
      router.push(paths.dashboard.root);
      // router.refresh();
    } catch (error) {
      console.error(error);
      setErrorMsg(error instanceof Error ? error.message : error);
    }
  });

  return (
    <Box sx={{ width:'50%' }}>
      <Box
        component="img"
        src={`${CONFIG.site.basePath}/logo/logo-single.svg`}
        sx={{ width: 53.53, height: 50, mb: 5 }}
      />
      <Typography variant="h1" textAlign="center" paragraph>
        {t('auth.register')}
      </Typography>

      <Typography variant="body1" textAlign="center" sx={{ color: 'text.secondary', mb: 5 }}>
        {t('auth.joinUsSubtitle')}
      </Typography>

      {!!errorMsg && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {errorMsg}
        </Alert>
      )}

      <Form methods={methods} onSubmit={onSubmit}>
        <Stack spacing={3} sx={{ width: '100%' }}>
          <Field.Text
            name="name"
            label={t('auth.name')}
            placeholder={t('auth.enterYourName')}
            fullWidth
          />

          <Field.Text
            name="email"
            label={t('auth.email')}
            placeholder={t('auth.enterYourEmail')}
            fullWidth
          />

          <Field.Text
            name="password"
            label={t('auth.password')}
            type={password.value ? 'text' : 'password'}
            placeholder={t('auth.enterYourPassword')}
            fullWidth
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={password.onToggle} edge="end">
                    <Iconify icon={password.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <AppButton
            label={t('auth.register')}
            fullWidth
            color="primary"
            size="large"
            type="submit"
            variant="contained"
            isLoading={isSubmitting}
          />

          <Stack direction="row" alignItems="center" spacing={2} sx={{ my: 1 }}>
            <Divider sx={{ flexGrow: 1 }}>{t('auth.orRegisterWith')}</Divider>
          </Stack>

          <Stack direction="row" spacing={2} justifyContent="center">

            <Avatar sx={{width:32, height:32}} alt="Google" src="/assets/icons/brands/ic-brand-google.svg" />


            <Avatar sx={{width:32, height:32}}  src="/assets/icons/brands/ic-brand-facebook.svg" alt="Facebook"  />


            <Avatar sx={{width:32, height:32}}  src="/assets/icons/brands/ic-brand-apple.svg" alt="Apple"  />
        </Stack>

          <Typography variant="body2" align="center" sx={{ color: 'text.secondary' }}>
            {t('auth.alreadyHaveAccount')}{' '}
            <Link component={RouterLink} href={paths.auth.jwt.signIn} variant="subtitle2">
              {t('auth.login')}
            </Link>
          </Typography>
        </Stack>
      </Form>

          <Stack direction="row" spacing={1} justifyContent="center" alignItems="center" sx={{ mt: 3 }}>
      <Link variant="body2" color="text.secondary" href="#">
        Privacy policy
      </Link>
      <Typography color="text.disabled" sx={{ fontSize: '30px' }}>•</Typography>
      <Link variant="body2" color="text.secondary" href="#">
        Terms of use
      </Link>
      <Typography color="text.disabled" sx={{ fontSize: '30px' }}>•</Typography>
      <Link variant="body2" color="text.secondary" href="#">
        DMCA
      </Link>
    </Stack>
    </Box>
  );
}
