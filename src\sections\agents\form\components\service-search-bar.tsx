import { InputAdornment, TextField, SxProps, Theme } from '@mui/material';
import { Iconify } from 'src/components/iconify';

interface ServiceSearchBarProps {
  query: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  sx?: SxProps<Theme>;
}

export default function ServiceSearchBar({
  query,
  onChange,
  placeholder = 'Search services...',
  sx,
}: ServiceSearchBarProps) {
  return (
    <TextField
      fullWidth
      value={query}
      onChange={onChange}
      placeholder={placeholder}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
          </InputAdornment>
        ),
      }}
      sx={{
        mb: 3,
        '& .MuiOutlinedInput-root': {
          borderRadius: '10x',
          background: '#FFF',
        },
        ...sx,
      }}
    />
  );
}
