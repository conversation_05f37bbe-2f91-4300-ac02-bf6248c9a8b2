import { z as zod } from 'zod';
import { useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useTheme } from '@mui/material/styles';

import { AppButton } from 'src/components/common';

import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';

import { Form, Field } from 'src/components/hook-form';
import { resetPassword } from 'src/auth/context/jwt';

import { AuthHeader } from './components/auth-header';

// ----------------------------------------------------------------------

export const ResetPasswordSchema = zod.object({
  email: zod
    .string()
    .min(1, { message: 'Email is required!' })
    .email({ message: 'Email must be a valid email address!' }),
});

export const VerificationCodeSchema = zod.object({
  code: zod.string().length(5),
});

export type ResetPasswordSchemaType = zod.infer<typeof ResetPasswordSchema>;
export type VerificationCodeSchemaType = zod.infer<typeof VerificationCodeSchema>;

// ----------------------------------------------------------------------

export function JwtResetPasswordView() {
  const { t } = useTranslation();
  const theme = useTheme();
  const router = useRouter();
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  const [codeSent, setCodeSent] = useState(false);

  const submitRef = useRef<HTMLButtonElement>(null);

  const emailMethods = useForm<ResetPasswordSchemaType>({
    resolver: zodResolver(ResetPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const codeMethods = useForm<VerificationCodeSchemaType>({
    resolver: zodResolver(VerificationCodeSchema),
    defaultValues: {
      code: '',
    },
  });

  const {
    handleSubmit: handleEmailSubmit,
    formState: { isSubmitting: isEmailSubmitting },
  } = emailMethods;

  const {
    handleSubmit: handleCodeSubmit,
    formState: { isSubmitting: isCodeSubmitting },
  } = codeMethods;

  // Function to determine email TextField color based on value
  const getEmailFieldSx = () => {
    const value = emailMethods.watch('email');
    return {
      '& .MuiOutlinedInput-root': {
        '& fieldset': {
          borderColor: value ? theme.palette.success.main : 'rgba(0, 0, 0, 0.23)',
        },
        '&:hover fieldset': {
          borderColor: value ? theme.palette.success.main : 'rgba(0, 0, 0, 0.23)',
        },
        '&.Mui-focused fieldset': {
          borderColor: value ? theme.palette.success.main : theme.palette.primary.main,
        },
      },
    };
  };

  const onEmailSubmit = handleEmailSubmit(async (data) => {
    try {
      await resetPassword({ email: data.email });

      // Create masked email for display
      const maskedEmail = 'e********@gmail.com';
      setCodeSent(true);
      setSuccessMsg(`We've sent a 5-digit code to your email ${maskedEmail}.`);
      setErrorMsg('');
    } catch (error) {
      // Error handling
      // console.error(error);
      setErrorMsg(error instanceof Error ? error.message : 'Something went wrong');
      setSuccessMsg('');
    }
  });

  const onCodeSubmit = handleCodeSubmit(async (_data) => {
    try {
      // Here you would verify the code with your backend
      // const verificationCode = data.code;
      // Verification code would be sent to backend

      // Show success message
      setSuccessMsg('Code verified successfully! Redirecting to password change page...');
      setErrorMsg('');

      // Redirect to the new password page after a short delay
      setTimeout(() => {
        router.push(paths.auth.jwt.newPassword);
      }, 1500);
    } catch (error) {
      // Error handling
      // console.error(error);
      setErrorMsg(error instanceof Error ? error.message : 'Invalid verification code');
      setSuccessMsg('');
    }
  });

  return (
    <>
      <AuthHeader />
      <Box sx={{
        padding: 2,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        maxWidth: '600px',
        margin: '0 auto'
      }}>
        <Box sx={{ width: 40, height: 40, mb: 5 }} />
        <Typography variant="h1" textAlign="center" paragraph sx={{ mb: 7 }}>
          {t('auth.resetPassword')}
        </Typography>

        {codeSent ? (
          <>
            <Typography variant="body1" textAlign="center" sx={{ color: 'text.secondary', mb: 7 }}>
              {t('auth.resetPasswordSubtitle')}
            </Typography>

            {!!errorMsg && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {errorMsg}
              </Alert>
            )}

            <Form methods={emailMethods} onSubmit={onEmailSubmit}>
              <Stack spacing={3}  sx={{ width: '20vw',mb: 5 }}>
                <Field.Text
                  name="email"
                  label={t('auth.email')}
                  placeholder={t('auth.enterYourEmail')}
                  fullWidth
                  sx={getEmailFieldSx()}
                />

                <AppButton
                  label={t('auth.send')}
                  fullWidth
                  color="primary"
                  size="large"
                  type="submit"
                  variant="contained"
                  isLoading={isEmailSubmitting}
                />
              </Stack>
            </Form>
            <Typography variant="body2" align="center" sx={{ color: 'text.secondary' }}>
              {t('auth.rememberPassword')} 
              <Link href={paths.auth.jwt.signIn} variant="subtitle2">
                {t('auth.login')}
              </Link>
            </Typography>
          </>
        ) : (
          <>
            {!!successMsg && (
              <Alert severity="success" sx={{ mb: 5 }}>
                {successMsg}
              </Alert>
            )}

            {!!errorMsg && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {errorMsg}
              </Alert>
            )}

            <Typography variant="body1" textAlign="center" sx={{ color: 'text.secondary', mb: 7 }}>
              {t('auth.codeSent', { email: 'e********@gmail.com' })}
              <br/>{t('auth.codeSentInstruction')}
            </Typography>

            <Form methods={codeMethods} onSubmit={onCodeSubmit} >
              <Stack spacing={3} sx={{ width: '100%', alignItems: 'center' }}>
                <Field.Code
                  name="code"
                  length={5}
                  sx={{
                    '& .MuiTextField-root': {
                      width: 60,
                      height: 90,
                    },
                    '& .MuiInputBase-root': {
                      height: '69px',
                    },
                    '& .MuiOutlinedInput-root': {
                      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      },
                      '&.Mui-error .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.error.main,
                      },
                    },
                  }}
                />

                <AppButton
                  label={isCodeSubmitting ? t('auth.checking') : t('auth.send')}
                  ref={submitRef}
                  fullWidth
                  color="primary"
                  size="large"
                  type="submit"
                  variant="contained"
                  isLoading={isCodeSubmitting}
                />
              </Stack>
            </Form>
          </>
        )}

        <Stack direction="row" spacing={1} justifyContent="center" alignItems="center" sx={{ mt: 4, mb: 2, position: 'relative' }}>
          <Link variant="body2" color="text.secondary" href="#">
            {t('common.privacyPolicy')}
          </Link>
          <Typography color="text.disabled" sx={{ fontSize: '30px' }}>
            •
          </Typography>
          <Link variant="body2" color="text.secondary" href="#">
            {t('common.termsOfUse')}
          </Link>
          <Typography color="text.disabled" sx={{ fontSize: '30px' }}>
            •
          </Typography>
          <Link variant="body2" color="text.secondary" href="#">
            {t('common.dmca')}
          </Link>
        </Stack>
      </Box>
    </>
  );
}
