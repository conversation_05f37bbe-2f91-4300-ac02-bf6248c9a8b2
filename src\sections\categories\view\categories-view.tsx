import { Box, Grid, Typography, useTheme, CircularProgress } from '@mui/material';
import { AppContainer } from 'src/components/common';
import { Iconify } from 'src/components/iconify';
import { paths } from 'src/routes/paths';
import { useTranslation } from 'react-i18next';
import CategoryCard from '../components/category-card';
import CategoryDialog from '../form/category-dialog';
import { useCategoriesView } from '../hooks/use-categories-view';

// ----------------------------------------------------------------------

const ViewAllCategories = () => {
  const theme = useTheme();
  const { t } = useTranslation();

  // Use our custom hook for categories
  const {
    categories,
    loading,
    error,
    openDialog,
    selectedCategory,
    handleCloseDialog,
    handleCreateCategory,
    handleUpdateCategory,
    handleDeleteCategory,
    handleEditCategory,
    handleOpenCreateDialog,
  } = useCategoriesView();

  // Handle form submission based on whether we're editing or creating
  const handleFormSubmit = (data: any) => {
    if (selectedCategory) {
      handleUpdateCategory(data);
    } else {
      handleCreateCategory(data);
    }
  };

  return (
    <AppContainer
      title={t('categories.title')}
      pageTitle={t('categories.title')}
      routeLinks={[
        {
          name: t('categories.dashboard'),
          href: paths.dashboard.root,
        },
        {
          name: t('categories.title'),
        },
      ]}
      buttons={[
        {
          label: t('categories.createNew'),
          variant: 'outlined',
          startIcon: <Iconify icon="majesticons:plus" />,
          onClick: handleOpenCreateDialog,
        },
      ]}
    >
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 10 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box sx={{ py: 10, textAlign: 'center' }}>
          <Typography variant="h6" color="error" paragraph>
            {error}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {t('categories.tryAgain')}
          </Typography>
        </Box>
      ) : categories.length > 0 ? (
        <Grid container spacing={3}>
          {categories.map((category) => (
            <Grid key={category.id} item xs={12} sm={6} md={6} lg={6} xl={6}>
              <CategoryCard
                id={category.id}
                name={category.name}
                description={category.description}
                agentsCount={category.agentsCount}
                icon={category.icon}
                color={category.colorType === 'custom' ? category.customColor : theme.palette[category.colorType].main}
                onDelete={handleDeleteCategory}
                onEdit={handleEditCategory}
              />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Box sx={{ py: 10, textAlign: 'center' }}>
          <Typography variant="h6" paragraph>
            {t('categories.noCategories')}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {t('categories.createFirst')}
          </Typography>
        </Box>
      )}

      {/* Category Dialog */}
      <CategoryDialog
        open={openDialog}
        onClose={handleCloseDialog}
        onSubmit={handleFormSubmit}
        defaultValues={selectedCategory || undefined}
      />
    </AppContainer>
  );
};

export default ViewAllCategories;
