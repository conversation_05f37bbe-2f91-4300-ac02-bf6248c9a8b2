import {
  <PERSON><PERSON>,
  Card,
  <PERSON>ack,
  Avatar,
  Typography,
  AvatarGroup,
  IconButton,
  Menu,
  MenuItem,
  Box,
} from '@mui/material';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import { paths } from 'src/routes/paths';
import { Label } from 'src/components/label';
import { AppButton, AppContainer } from 'src/components/common';
import { fDate } from 'src/utils/format-time';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';
import { useTeamsView, TeamData, TeamMember } from './use-teams-view';
import TeamForm from '../form/team-form';


// ----------------------------------------------------------------------

// Mock data for team members
const MOCK_MEMBERS: TeamMember[] = [
  {
    id: '1',
    name: '<PERSON>',
    avatarUrl: '/assets/images/avatar/avatar_1.jpg',
    role: 'Team Lead',
  },
  {
    id: '2',
    name: '<PERSON>',
    avatarUrl: '/assets/images/avatar/avatar_2.jpg',
    role: 'Developer',
  },
  {
    id: '3',
    name: 'Michael <PERSON>',
    avatarUrl: '/assets/images/avatar/avatar_3.jpg',
    role: 'Designer',
  },
  {
    id: '4',
    name: 'Emily Williams',
    avatarUrl: '/assets/images/avatar/avatar_4.jpg',
    role: 'Marketing',
  },
  {
    id: '5',
    name: 'David Brown',
    avatarUrl: '/assets/images/avatar/avatar_5.jpg',
    role: 'Support',
  },
];

// Mock data for teams
const MOCK_TEAMS: TeamData[] = [
  {
    id: '1',
    name: 'Product Development',
    description: 'Team responsible for developing new product features and improvements',
    type: 'Development',
    members: MOCK_MEMBERS.slice(0, 4),
    createdAt: new Date('2023-01-15'),
  },
  {
    id: '2',
    name: 'Customer Support',
    description: 'Team handling customer inquiries and technical support',
    type: 'Support',
    members: MOCK_MEMBERS.slice(1, 3),
    createdAt: new Date('2023-02-20'),
  },
  {
    id: '3',
    name: 'Marketing',
    description: 'Team responsible for marketing campaigns and brand awareness',
    type: 'Marketing',
    members: MOCK_MEMBERS.slice(2, 5),
    createdAt: new Date('2023-03-10'),
  },
  {
    id: '4',
    name: 'Sales',
    description: 'Team handling sales operations and client relationships',
    type: 'Sales',
    members: MOCK_MEMBERS.slice(0, 2),
    createdAt: new Date('2023-04-05'),
  },
  {
    id: '5',
    name: 'Design',
    description: 'Team responsible for UI/UX design and visual assets',
    type: 'Design',
    members: MOCK_MEMBERS.slice(1, 5),
    createdAt: new Date('2023-05-12'),
  },
  {
    id: '6',
    name: 'HR',
    description: 'Team handling human resources and employee relations',
    type: 'HR',
    members: MOCK_MEMBERS.slice(3, 5),
    createdAt: new Date('2023-06-18'),
  },
];

// Get color based on team type
const getTeamTypeColor = (type: string) => {

  switch (type) {
    case 'Development':
      return 'primary';
    case 'Support':
      return 'info';
    case 'Marketing':
      return 'warning';
    case 'Sales':
      return 'success';
    case 'Design':
      return 'error';
    case 'HR':
      return 'secondary';
    default:
      return 'default';
  }
};

export const TeamsView = () => {
  const { t } = useTranslation();

  // Menu state
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [menuTeamId, setMenuTeamId] = useState<string | null>(null);
  const openMenu = Boolean(menuAnchorEl);

  // Use the custom hook to manage teams
  const {
    teams,
    selectedTeam,
    openConfirmDialog,
    formDialog,
    handleAddTeam,
    handleEditTeam,
    handleViewTeam,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleDeleteTeam,
    handleFormSubmit,
  } = useTeamsView(MOCK_TEAMS);

  // Handle opening the menu
  const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>, id: string) => {
    setMenuAnchorEl(event.currentTarget);
    setMenuTeamId(id);
  };

  // Handle closing the menu
  const handleCloseMenu = () => {
    setMenuAnchorEl(null);
    setMenuTeamId(null);
  };

  return (
    <AppContainer
      title={t('components.teams.title')}
      routeLinks={[{ name: t('components.dashboard.overView'), href: paths.dashboard.root }, { name: t('components.teams.title') }]}
      buttons={[
        {
          label: t('components.buttons.createNewTeam'),
          variant: 'outlined',
          startIcon: <Iconify icon="eva:plus-fill" />,
          onClick: handleAddTeam,
        },
      ]}
    >
      <Grid container spacing={3}>
        {teams.map((team) => (
          <Grid item xs={12} sm={6} md={4} key={team.id}>
            <Card sx={{ p: 3, height: '291px' }}>
              <Stack spacing={2}>
                {/* Card Header - Avatars and Menu */}
                <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                  <AvatarGroup
                    max={3}
                    sx={{
                      '& .MuiAvatar-root': {
                        width: 40,
                        height: 40,
                        fontSize: '1rem',
                        border: (theme) => `2px solid ${theme.palette.background.paper}`,
                      },
                    }}
                  >
                    {team.members.map((member) => (
                      <Avatar
                        key={member.id}
                        alt={member.name}
                        src={member.avatarUrl}
                        title={member.name}
                      />
                    ))}
                  </AvatarGroup>

                  <IconButton
                    size="small"
                    onClick={(event) => handleOpenMenu(event, team.id)}
                    sx={{ ml: 1 }}
                  >
                    <Iconify icon="eva:more-vertical-fill" />
                  </IconButton>
                </Stack>

                {/* Team Name and Type */}
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Typography variant="h6" noWrap>
                    {team.name}
                  </Typography>
                  <Label variant="soft" color={getTeamTypeColor(team.type)}>
                    {team.type}
                  </Label>
                </Stack>

                {/* Team Description */}
                <Typography variant="body2" sx={{ color: 'text.secondary', flexGrow: 1 }}>
                  {team.description}
                </Typography>

                {/* Team Info */}
                <Stack direction="row" alignItems="center" spacing={1} sx={{ pt: 1 }}>
                  <Iconify
                    icon="eva:people-fill"
                    sx={{ color: 'text.disabled', width: 16, height: 16 }}
                  />
                  <Typography variant="caption" sx={{ color: 'text.disabled' }}>
                    {team.members.length} {team.members.length === 1 ? t('components.common.member') : t('components.common.members')}
                  </Typography>
                  <Box sx={{ flexGrow: 1 }} />
                  <Typography variant="caption" sx={{ color: 'text.disabled' }}>
                    {t('components.common.created')} {fDate(team.createdAt)}
                  </Typography>
                </Stack>
              </Stack>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Team Form Dialog */}
      {formDialog.value && (
        <TeamForm
          open={formDialog.value}
          onClose={formDialog.onFalse}
          team={selectedTeam}
          onSubmit={handleFormSubmit}
          availableMembers={MOCK_MEMBERS}
        />
      )}

      {/* Actions Menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={openMenu}
        onClose={handleCloseMenu}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <MenuItem
          onClick={() => {
            if (menuTeamId) handleViewTeam(menuTeamId);
            handleCloseMenu();
          }}
        >
          <Iconify icon="eva:eye-fill" sx={{ mr: 2, width: 20, height: 20 }} />
          {t('components.common.view')}
        </MenuItem>
        <MenuItem
          onClick={() => {
            if (menuTeamId) handleEditTeam(menuTeamId);
            handleCloseMenu();
          }}
        >
          <Iconify icon="eva:edit-fill" sx={{ mr: 2, width: 20, height: 20 }} />
          {t('components.common.edit')}
        </MenuItem>
        <MenuItem
          onClick={() => {
            if (menuTeamId) handleOpenConfirmDialog(menuTeamId);
            handleCloseMenu();
          }}
          sx={{ color: 'error.main' }}
        >
          <Iconify icon="eva:trash-2-outline" sx={{ mr: 2, width: 20, height: 20 }} />
          {t('components.buttons.delete')}
        </MenuItem>
      </Menu>

      {/* Confirm Delete Dialog */}
      <ConfirmDialog
        open={openConfirmDialog}
        onClose={handleCloseConfirmDialog}
        close={handleCloseConfirmDialog}
        title={
          <Typography variant="h3" textAlign="center">
            {t('components.dialogs.deleteTeam')}
          </Typography>
        }
        content={
          <Typography variant="body1">
            {t('components.dialogs.deleteTeamConfirm')}
          </Typography>
        }
        icon={
          <Box sx={{ textAlign: 'center' }}>
            <Iconify
              icon="eva:alert-triangle-fill"
              sx={{ width: 64, height: 64, color: 'error.main' }}
            />
          </Box>
        }
        action={
          <Stack direction="row" justifyContent="center" spacing={1} sx={{ width: '100%' }}>
            <AppButton
              sx={{ width: '45%' }}
              label={t('components.buttons.delete')}
              variant="contained"
              color="error"
              onClick={handleDeleteTeam}
            />
            <AppButton
              sx={{ width: '45%' }}
              label={t('components.buttons.cancel')}
              variant="outlined"
              color="inherit"
              onClick={handleCloseConfirmDialog}
            />
          </Stack>
        }
      />
    </AppContainer>
  );
};
