import { AxiosError } from 'axios';
import { enqueueSnackbar } from 'notistack';
import { useTranslation } from 'react-i18next';

export const useApiResult = () => {
  const { t } = useTranslation();

  const currentLng = localStorage.getItem('i18nextLng') || 'en';

  const handleApiSuccessWithSnackbar = () => {
    enqueueSnackbar({
      variant: 'success',
      message: t('common.successResult'),
      anchorOrigin: {
        vertical: 'bottom',
        horizontal: currentLng === 'en' ? 'right' : 'left',
      },
    });
  };

  const handleApiErrorWithSnackbar = (error: AxiosError) => {
    enqueueSnackbar({
      variant: 'error',
      message: error?.message,
      anchorOrigin: {
        vertical: 'top',
        horizontal: currentLng === 'en' ? 'right' : 'left',
      },
    });
  };

  return {
    handleApiSuccessWithSnackbar,
    handleApiErrorWithSnackbar,
  };
};

export default useApiResult;
