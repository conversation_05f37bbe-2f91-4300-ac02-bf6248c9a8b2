import { useState, useCallback, useMemo } from 'react';
import { Stack, Avatar, Typography, Chip } from '@mui/material';
import useTable from 'src/components/table/use-table';
import { AppTablePropsType } from 'src/components/table';
import { Label } from 'src/components/label';
import LongMenu from 'src/components/long-menu';

// Manager data type
export interface ManagerData {
  id: string;
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  avatarUrl: string;
  status: string;
  role: string;
}

// Form values type
export interface ManagerFormValues {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password?: string;
  confirmPassword?: string;
}

// Status filter options
export const STATUS_TAB_OPTIONS = [
  { label: 'All', value: 'all' },
  { label: 'Active', value: 'active' },
  { label: 'Pending', value: 'pending' },
  { label: 'Banned', value: 'banned' },
  { label: 'Rejected', value: 'rejected' },
];

export function useManagersView(initialManagers: ManagerData[]) {
  const [managers, setManagers] = useState<ManagerData[]>(initialManagers);
  const [selectedManager, setSelectedManager] = useState<ManagerData | null>(null);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [openLoginConfirmDialog, setOpenLoginConfirmDialog] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [menuPosition, setMenuPosition] = useState<null | { top: number; left: number }>(null);
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const [selectedManagerForLogin, setSelectedManagerForLogin] = useState<ManagerData | null>(null);

  // Filter and search state
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Table settings
  const table = useTable({
    defaultOrderBy: 'firstName',
  });

  // Handle opening the menu
  const handleOpenMenu = useCallback((event: React.MouseEvent<HTMLButtonElement>, id: string) => {
    event.stopPropagation();
    setMenuPosition({
      left: event.clientX,
      top: event.clientY,
    });
    setSelectedId(id);
  }, []);

  // Handle closing the menu
  const handleCloseMenu = useCallback(() => {
    setMenuPosition(null);
  }, []);

  // Handle viewing a manager
  const handleViewManager = useCallback(
    (id: string) => {
      // Implement view functionality here
      console.log(`Viewing manager with ID: ${id}`);
      handleCloseMenu();
    },
    [handleCloseMenu]
  );

  // Handle editing a manager
  const handleEditManager = useCallback(
    (id: string) => {
      const manager = managers.find((m) => m.id === id);
      if (manager) {
        setSelectedManager(manager);
        // Navigate to edit page instead of opening a dialog
        window.location.href = `/dashboard/managers/edit/${id}`;
      }
      handleCloseMenu();
    },
    [managers, handleCloseMenu]
  );

  // Handle opening the confirm dialog for deleting managers
  const handleOpenConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(true);
  }, []);

  // Handle closing the confirm dialog
  const handleCloseConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(false);
  }, []);

  // Handle opening the login confirm dialog
  const handleOpenLoginConfirmDialog = useCallback((id: string) => {
    const manager = managers.find((m) => m.id === id);
    if (manager) {
      setSelectedManagerForLogin(manager);
      setOpenLoginConfirmDialog(true);
    }
  }, [managers]);

  // Handle closing the login confirm dialog
  const handleCloseLoginConfirmDialog = useCallback(() => {
    setOpenLoginConfirmDialog(false);
    setSelectedManagerForLogin(null);
  }, []);

  // Handle deleting selected managers
  const handleDeleteManagers = useCallback(() => {
    setManagers((prev) => prev.filter((manager) => !selectedIds.includes(manager.id)));
    table.setSelected([]);
    setSelectedIds([]);
    setOpenConfirmDialog(false);
  }, [selectedIds, table]);

  // Handle deleting a single manager from menu
  const handleDeleteSingleManager = useCallback(() => {
    if (selectedId) {
      setManagers((prev) => prev.filter((manager) => manager.id !== selectedId));
      setOpenConfirmDialog(false);
    }
    handleCloseMenu();
  }, [selectedId, handleCloseMenu]);

  // Handle selecting a row
  const handleSelectRow = useCallback(
    (row: ManagerData, table: any) => {
      table.onSelectRow(row?.id);

      // Update selectedIds based on table.selected
      if (table.selected.includes(row?.id)) {
        setSelectedIds((prev) => [...prev, row?.id]);
      } else {
        setSelectedIds((prev) => prev.filter((selectedId) => selectedId !== row?.id));
      }
    },
    [table]
  );

  // Handle selecting all rows
  const handleSelectAllRows = useCallback(
    (ids: string[]) => (checked: boolean) => {
      table.onSelectAllRows(checked, ids);

      // Update selectedIds based on checked state
      if (checked) {
        setSelectedIds(ids);
      } else {
        setSelectedIds([]);
      }
    },
    [table]
  );

  // Handle status filter change
  const handleStatusFilterChange = useCallback((newStatus: string) => {
    setStatusFilter(newStatus);
  }, []);

  // Handle search query change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  }, []);

  // Handle login as manager
  const handleLoginAsManager = useCallback((id: string) => {
    handleOpenLoginConfirmDialog(id);
    handleCloseMenu();
  }, [handleCloseMenu, handleOpenLoginConfirmDialog]);

  // Execute login as manager after confirmation
  const executeLoginAsManager = useCallback(() => {
    if (selectedManagerForLogin) {
      // Implement login as functionality here
      console.log(`Logging in as manager with ID: ${selectedManagerForLogin.id}`);
      handleCloseLoginConfirmDialog();
    }
  }, [selectedManagerForLogin, handleCloseLoginConfirmDialog]);

  // Filter managers based on status and search query
  const filteredManagers = useMemo(() => {
    return managers.filter((manager) => {
      // Filter by status
      if (statusFilter !== 'all' && manager.status !== statusFilter) {
        return false;
      }

      // Filter by search query
      if (searchQuery) {
        const fullName = `${manager.firstName} ${manager.lastName}`.toLowerCase();
        const email = manager.email.toLowerCase();
        const query = searchQuery.toLowerCase();
        return fullName.includes(query) || email.includes(query);
      }

      return true;
    });
  }, [managers, statusFilter, searchQuery]);

  // Handle form submission
  const handleFormSubmit = useCallback(
    (data: ManagerFormValues, id?: string) => {
      if (id) {
        // Edit existing manager
        setManagers((prev) =>
          prev.map((manager) =>
            manager.id === id
              ? {
                  ...manager,
                  firstName: data.firstName,
                  lastName: data.lastName,
                  username: data.username,
                  email: data.email,
                }
              : manager
          )
        );
      } else {
        // Add new manager
        const newManager = {
          id: String(managers.length + 1),
          firstName: data.firstName,
          lastName: data.lastName,
          username: data.username,
          email: data.email,
          avatarUrl: '/assets/images/avatar/avatar_default.jpg', // Default avatar
          status: 'active',
          role: 'Manager',
        } as ManagerData;
        setManagers((prev) => [...prev, newManager]);
      }
    },
    [managers]
  );

  const MENU_OPTIONS = (row: ManagerData) => [
    {
      label: 'View',
      icon: 'eva:eye-fill',
      onClick: () => handleViewManager(row.id),
      color: 'inherit',
    },
    {
      label: 'Edit',
      icon: 'eva:edit-fill',
      onClick: () => handleEditManager(row.id),
      color: 'inherit',
    },
    {
      label: 'Login as',
      icon: 'eva:person-done-fill',
      onClick: () => handleLoginAsManager(row.id),
      color: 'inherit',
    },
    {
      label: 'Delete',
      icon: 'eva:trash-2-outline',
      onClick: () => {
        setSelectedId(row.id);
        handleOpenConfirmDialog();
      },
      color: 'error.main',
    },
  ];

  const columns: AppTablePropsType<ManagerData>['columns'] = [
    {
      name: 'firstName',
      PreviewComponent: (data) => {
        const { avatarUrl, firstName, lastName, email, username } = data;
        return (
          <Stack direction="row" alignItems="center" spacing={2}>
            <Avatar src={avatarUrl} alt={`${firstName} ${lastName}`} />
            <Stack spacing={0.5}>
              <Typography variant="subtitle2" noWrap>
                {`${firstName} ${lastName}`}
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
                {email}
              </Typography>
              <Typography variant="caption" sx={{ color: 'text.disabled' }} noWrap>
                @{username}
              </Typography>
            </Stack>
          </Stack>
        );
      },
    },
    {
      name: 'role',
      PreviewComponent: (data) => {
        const { role } = data;
        return (
          <Label variant="soft" color={role === 'Admin' ? 'error' : 'info'}>
            {role}
          </Label>
        );
      },
    },
    {
      name: 'status',
      PreviewComponent: (data) => {
        const { status } = data;
        let color: 'success' | 'warning' | 'error' | 'info' | 'default' = 'default';

        switch (status) {
          case 'active':
            color = 'success';
            break;
          case 'pending':
            color = 'warning';
            break;
          case 'banned':
            color = 'error';
            break;
          case 'rejected':
            color = 'info';
            break;
          default:
            color = 'default';
        }

        return (
          <Chip
            label={status.charAt(0).toUpperCase() + status.slice(1)}
            color={color}
            size="small"
            variant="outlined"
          />
        );
      },
    },
    {
      name: 'id',
      cellSx: { width: '50px' },
      PreviewComponent: (row) => {
        return <LongMenu options={MENU_OPTIONS(row)} />;
      },
    },
  ];

  return {
    // State
    managers,
    filteredManagers,
    selectedManager,
    openConfirmDialog,
    openLoginConfirmDialog,
    selectedIds,
    menuPosition,
    selectedId,
    selectedManagerForLogin,
    openMenu: Boolean(menuPosition),
    table,
    columns,
    statusFilter,
    searchQuery,
    // Handlers
    handleOpenMenu,
    handleCloseMenu,
    handleViewManager,
    handleEditManager,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleOpenLoginConfirmDialog,
    handleCloseLoginConfirmDialog,
    handleDeleteManagers,
    handleDeleteSingleManager,
    handleSelectRow,
    handleSelectAllRows,
    handleFormSubmit,
    handleStatusFilterChange,
    handleSearchChange,
    handleLoginAsManager,
    executeLoginAsManager,
  };
}
