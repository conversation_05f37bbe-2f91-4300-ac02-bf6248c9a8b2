import { Helmet } from 'react-helmet-async';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Container } from '@mui/material';
import { useParams } from 'src/routes/hooks';
import { useManagersView, ManagerData } from 'src/sections/managers/view/use-managers-view';
import ManagerForm from 'src/sections/managers/form/manager-form';

// Mock data for managers (in a real app, you would fetch this from an API)
const MOCK_MANAGERS: ManagerData[] = [
  {
    id: '1',
    firstName: '<PERSON>',
    lastName: 'Doe',
    username: 'johndo<PERSON>',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_1.jpg',
    status: 'active',
    role: 'Admin',
  },
  {
    id: '2',
    firstName: 'Jane',
    lastName: '<PERSON>',
    username: 'jane<PERSON>',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_2.jpg',
    status: 'active',
    role: 'Manager',
  },
  {
    id: '3',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    username: 'micha<PERSON><PERSON>',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_3.jpg',
    status: 'inactive',
    role: 'Manager',
  },
  {
    id: '4',
    firstName: 'Emily',
    lastName: 'Williams',
    username: 'emilyw',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_4.jpg',
    status: 'active',
    role: 'Manager',
  },
  {
    id: '5',
    firstName: 'David',
    lastName: 'Brown',
    username: 'davidb',
    email: '<EMAIL>',
    avatarUrl: '/assets/images/avatar/avatar_5.jpg',
    status: 'active',
    role: 'Manager',
  },
];

// ----------------------------------------------------------------------

export default function EditManagerPage() {
  const { t } = useTranslation();
  const { id } = useParams();
  const [manager, setManager] = useState<ManagerData | null>(null);

  // Use the managers view hook to get the form submission handler
  const { handleFormSubmit } = useManagersView(MOCK_MANAGERS);

  // Fetch manager data based on ID
  useEffect(() => {
    // In a real app, you would fetch this from an API
    const foundManager = MOCK_MANAGERS.find((m) => m.id === id) || null;
    setManager(foundManager);
  }, [id]);

  return (
    <>
      <Helmet>
        <title>{`${t('pages.dashboard.title')}: ${t('pages.dashboard.editManager')}`}</title>
      </Helmet>

      <Container maxWidth="lg">
        {manager && <ManagerForm manager={manager} onSubmit={handleFormSubmit} />}
      </Container>
    </>
  );
}
