import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, InputAdornment, IconButton } from '@mui/material';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';
import * as z from 'zod';
import { Form } from 'src/components/hook-form/form-provider';
import { Field } from 'src/components/hook-form/fields';
import { AppButton } from 'src/components/common';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

// Form validation schema with translations
const createProfileSchema = (t: any) => z
  .object({
    firstName: z.string().min(1, t('profile.validation.firstNameRequired')),
    lastName: z.string().min(1, t('profile.validation.lastNameRequired')),
    email: z.string().email(t('profile.validation.emailValid')),
    username: z.string().min(3, t('profile.validation.usernameLength')),
    password: z
      .string()
      .min(8, t('profile.validation.passwordLength'))
      .regex(/[A-Z]/, t('profile.validation.passwordUppercase'))
      .regex(/[a-z]/, t('profile.validation.passwordLowercase'))
      .regex(/[0-9]/, t('profile.validation.passwordNumber'))
      .optional()
      .or(z.literal('')),
    confirmPassword: z.string().optional().or(z.literal('')),
  })
  .refine(
    (data) => {
      // If password is provided, confirmPassword must match
      if (data.password && data.password !== data.confirmPassword) {
        return false;
      }
      return true;
    },
    {
      message: t('profile.validation.passwordsMatch'),
      path: ['confirmPassword'],
    }
  );

// Form values type
type ProfileFormValues = z.infer<ReturnType<typeof createProfileSchema>>;

// Mock user data
const mockUserData = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  username: 'johndoe',
  password: '',
  confirmPassword: '',
};

export default function MyProfileTab() {
  const { t } = useTranslation();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Initialize form with default values
  const methods = useForm<ProfileFormValues>({
    mode: 'onChange',
    resolver: zodResolver(createProfileSchema(t)),
    defaultValues: mockUserData,
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  // Handle form submission
  const onSubmit = async (data: ProfileFormValues) => {
    try {
      // In a real app, you would send this data to an API
      console.log('Profile data submitted:', data);

      // Show success message
      alert(t('profile.profileUpdated'));
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    methods.reset(mockUserData);
  };

  return (
    <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Field.Text name="firstName" label={t('profile.firstName')} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Field.Text name="lastName" label={t('profile.lastName')} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Field.Text name="email" label={t('profile.email')} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Field.Text name="username" label={t('profile.username')} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Field.Text
            name="password"
            label={t('profile.password')}
            type={showPassword ? 'text' : 'password'}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={() => setShowPassword(!showPassword)} edge="end">
                    <Iconify icon={showPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            helperText={t('profile.keepCurrentPassword')}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Field.Text
            name="confirmPassword"
            label={t('profile.confirmPassword')}
            type={showConfirmPassword ? 'text' : 'password'}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    edge="end"
                  >
                    <Iconify icon={showConfirmPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Stack direction="row" justifyContent="flex-end" spacing={2}>
            <AppButton label={t('buttons.save')} type="submit" variant="contained" isLoading={isSubmitting} />
            <AppButton label={t('buttons.cancel')} variant="outlined" color="inherit" onClick={handleCancel} />
          </Stack>
        </Grid>
      </Grid>
    </Form>
  );
}
