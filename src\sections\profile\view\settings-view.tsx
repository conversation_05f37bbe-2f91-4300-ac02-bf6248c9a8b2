import { useState, useEffect } from 'react';
import { Box, Card, Container, Typography } from '@mui/material';
import { useColorScheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import { useSettingsContext } from 'src/components/settings';

// ----------------------------------------------------------------------

export function SettingsView() {
  const settings = useSettingsContext();
  const { mode, setMode } = useColorScheme();
  const { t, i18n } = useTranslation();

  // For UI state, we'll use local states that sync with the actual settings
  const [language, setLanguage] = useState(settings.direction === 'rtl' ? 'arabic' : 'english');
  const [selectedTheme, setSelectedTheme] = useState<'light' | 'dark' | 'system'>(
    mode === 'system' ? 'system' : settings.colorScheme
  );

  // Synchronize the selectedTheme state with mode and settings.colorScheme
  useEffect(() => {
    setSelectedTheme(mode === 'system' ? 'system' : settings.colorScheme);
  }, [mode, settings.colorScheme]);

  // Synchronize the language state with the direction setting
  useEffect(() => {
    setLanguage(settings.direction === 'rtl' ? 'arabic' : 'english');
  }, [settings.direction]);

  // Function to change language
  const changeLanguage = (lang: string) => {
    const newLang = lang === 'arabic' ? 'ar' : 'en';
    i18n.changeLanguage(newLang);
    console.log('Language changed to:', newLang);
  };

  return (
    <>
      <Container maxWidth="lg">
        <Card sx={{ p: 5 }}>
          <Typography variant="h4" sx={{ mb: 2 }}>
            {t('components.accountMenu.settings')}
          </Typography>

          <Typography variant="body1" color="text.secondary" sx={{ mb: 5 }}>
            {t('components.profile.settings.managePreferences')}
          </Typography>



          {/* Language */}
          <Box sx={{ mb: 5 }}>
            <Box
              sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}
            >
              <Box>
                <Typography variant="h6" gutterBottom>
                  {t('components.profile.settings.language')}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {t('components.profile.settings.chooseLanguage')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    px: 2,
                    py: 1,
                    borderRadius: 2,
                    bgcolor: language === 'english' ? 'rgba(255, 107, 53, 0.08)' : 'transparent',
                    border: '1px solid',
                    borderColor: language === 'english' ? '#FF6B35' : '#e0e0e0',
                    cursor: 'pointer',
                    '&:hover': {
                      bgcolor:
                        language === 'english' ? 'rgba(255, 107, 53, 0.12)' : 'rgba(0, 0, 0, 0.04)',
                    },
                  }}
                  onClick={() => {
                    setLanguage('english');
                    settings.onUpdateField('direction', 'ltr');
                    changeLanguage('english');
                  }}
                >
                  <Iconify icon="emojione:flag-for-united-states" width={20} sx={{ mr: 1 }} />
                  <Typography color={language === 'english' ? '#FF6B35' : 'text.primary'}>
                    {t('components.profile.settings.languageOptions.english')}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    px: 2,
                    py: 1,
                    borderRadius: 2,
                    bgcolor: language === 'arabic' ? 'rgba(255, 107, 53, 0.08)' : 'transparent',
                    border: '1px solid',
                    borderColor: language === 'arabic' ? '#FF6B35' : '#e0e0e0',
                    cursor: 'pointer',
                    '&:hover': {
                      bgcolor:
                        language === 'arabic' ? 'rgba(255, 107, 53, 0.12)' : 'rgba(0, 0, 0, 0.04)',
                    },
                  }}
                  onClick={() => {
                    setLanguage('arabic');
                    settings.onUpdateField('direction', 'rtl');
                    changeLanguage('arabic');
                  }}
                >
                  <Iconify icon="emojione:flag-for-saudi-arabia" width={20} sx={{ mr: 1 }} />
                  <Typography color={language === 'arabic' ? '#FF6B35' : 'text.primary'}>
                    {t('components.profile.settings.languageOptions.arabic')}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>

          {/* Theme */}
          <Box>
            <Box
              sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}
            >
              <Box>
                <Typography variant="h6" gutterBottom>
                  {t('components.profile.settings.theme')}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {t('components.profile.settings.chooseTheme')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    px: 2,
                    py: 1,
                    borderRadius: 2,
                    bgcolor: selectedTheme === 'light' ? 'rgba(255, 107, 53, 0.08)' : 'transparent',
                    border: '1px solid',
                    borderColor: selectedTheme === 'light' ? '#FF6B35' : '#e0e0e0',
                    cursor: 'pointer',
                    '&:hover': {
                      bgcolor:
                        selectedTheme === 'light'
                          ? 'rgba(255, 107, 53, 0.12)'
                          : 'rgba(0, 0, 0, 0.04)',
                    },
                  }}
                  onClick={() => {
                    settings.onUpdateField('colorScheme', 'light');
                    setMode('light');
                    setSelectedTheme('light');
                  }}
                >
                  <Iconify
                    icon="solar:sun-bold"
                    width={20}
                    sx={{ mr: 1, color: selectedTheme === 'light' ? '#FF6B35' : 'text.primary' }}
                  />
                  <Typography color={selectedTheme === 'light' ? '#FF6B35' : 'text.primary'}>
                    {t('components.profile.settings.light')}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    px: 2,
                    py: 1,
                    borderRadius: 2,
                    bgcolor: selectedTheme === 'dark' ? 'rgba(255, 107, 53, 0.08)' : 'transparent',
                    border: '1px solid',
                    borderColor: selectedTheme === 'dark' ? '#FF6B35' : '#e0e0e0',
                    cursor: 'pointer',
                    '&:hover': {
                      bgcolor:
                        selectedTheme === 'dark'
                          ? 'rgba(255, 107, 53, 0.12)'
                          : 'rgba(0, 0, 0, 0.04)',
                    },
                  }}
                  onClick={() => {
                    settings.onUpdateField('colorScheme', 'dark');
                    setMode('dark');
                    setSelectedTheme('dark');
                  }}
                >
                  <Iconify
                    icon="ph:moon-fill"
                    width={20}
                    sx={{ mr: 1, color: selectedTheme === 'dark' ? '#FF6B35' : 'text.primary' }}
                  />
                  <Typography color={selectedTheme === 'dark' ? '#FF6B35' : 'text.primary'}>
                    {t('components.profile.settings.dark')}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    px: 2,
                    py: 1,
                    borderRadius: 2,
                    bgcolor:
                      selectedTheme === 'system' ? 'rgba(255, 107, 53, 0.08)' : 'transparent',
                    border: '1px solid',
                    borderColor: selectedTheme === 'system' ? '#FF6B35' : '#e0e0e0',
                    cursor: 'pointer',
                    '&:hover': {
                      bgcolor:
                        selectedTheme === 'system'
                          ? 'rgba(255, 107, 53, 0.12)'
                          : 'rgba(0, 0, 0, 0.04)',
                    },
                  }}
                  onClick={() => {
                    // For system theme, we set the mode to 'system' but keep the settings colorScheme as is
                    setMode('system');
                    setSelectedTheme('system');
                  }}
                >
                  <Iconify
                    icon="mdi:desktop-mac"
                    width={20}
                    sx={{ mr: 1, color: selectedTheme === 'system' ? '#FF6B35' : 'text.primary' }}
                  />
                  <Typography color={selectedTheme === 'system' ? '#FF6B35' : 'text.primary'}>
                    {t('components.profile.settings.system')}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </Card>
      </Container>
    </>
  );
}
