import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  <PERSON>alogContent,
  <PERSON>alog<PERSON>ctions,
  <PERSON>ack,
  IconButton,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>abe<PERSON>,
  StepContent,
  Typography,
  Box,
  Grid,
  Radio,
  Card,
  CardActionArea,
  CardContent,
  alpha,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { Field } from 'src/components/hook-form/fields';
import { Form } from 'src/components/hook-form/form-provider';
import { AppButton } from 'src/components/common';
import { useAgentForm } from './use-agent-form';
import ServiceSearchBar from './components/service-search-bar';

// ----------------------------------------------------------------------

// Component props
interface AgentFormProps {
  open: boolean;
  onClose: () => void;
  agent: {
    id: string;
    name: string;
    description: string;
    systemMessage?: string;
    email: string;
    avatarUrl: string;
    category: string;
    status: string;
    tools?: string[];
  } | null;
  onSubmit: (data: any) => void;
}

export default function AgentForm({ open, onClose, agent, onSubmit }: AgentFormProps) {
  // Use the custom hook for form logic
  const {
    theme,
    activeStep,
    methods,
    selectedTools,
    isSubmitting,
    handleNext,
    handleBack,
    handleToolToggle,
    onFormSubmit,
    handleSubmit,
    searchQuery,
    handleSearchChange,
    availableTools,
  } = useAgentForm({ agent, onSubmit });

  // Define the step content
  const steps = [
    {
      label: 'Agent Info',
      icon: 'mdi:account-tie',
      description: 'Enter the basic information about the agent',
      fields: (
        <Stack spacing={3}>
          <Field.Text name="name" label="Agent Name" />
          <Field.Text multiline rows={10} name="description" label="Description" />
          <Field.Text
            name="systemMessage"
            label="System Message"
            multiline
            rows={10}
            helperText="Instructions for the agent's behavior"
          />
        </Stack>
      ),
    },
    {
      label: 'Agent Tools',
      icon: 'mdi:tools',
      description: 'Select the tools this agent can use',
      fields: (
        <Box>
          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
            <Typography variant="subtitle2">
              Connected Services
            </Typography>
          </Stack>

          <ServiceSearchBar
            query={searchQuery}
            onChange={handleSearchChange}
            placeholder="Search services..."
          />

          <Grid container spacing={2}>
            {availableTools.map((tool) => {
              const isSelected = selectedTools.includes(tool.id);
              return (
                <Grid item xs={6} sm={4} md={6} key={tool.id}>
                  <Card
                    sx={{
                      bgcolor: isSelected ? alpha(tool.color, 0.1) : 'grey.100',
                      borderColor: isSelected ? tool.color : 'transparent',
                      borderWidth: 1,
                      borderStyle: 'solid',
                      transition: theme.transitions.create(['background-color', 'border-color'], {
                        duration: theme.transitions.duration.shorter,
                      }),
                    }}
                  >
                    <CardActionArea onClick={() => handleToolToggle(tool.id)}>
                      <CardContent>
                        <Stack alignItems="center" flexDirection="row" spacing={1}>
                          <Iconify
                            icon={tool.icon}
                            width={32}
                            height={32}
                            sx={{
                              color: isSelected ? tool.color : 'text.secondary',
                              mb: 1,
                            }}
                          />
                          <Typography
                            variant="subtitle2"
                            sx={{
                              color: isSelected ? 'text.primary' : 'text.secondary',
                              textAlign: 'center',
                            }}
                          >
                            {tool.name}
                          </Typography>
                        </Stack>
                      </CardContent>
                    </CardActionArea>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        </Box>
      ),
    },
  ];

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
      <DialogTitle sx={{ pb: 2 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          {agent ? 'Edit Agent' : 'Add Agent'}
          <IconButton onClick={onClose}>
            <Iconify icon="material-symbols:cancel-outline" />
          </IconButton>
        </Stack>
      </DialogTitle>

      <Form methods={methods} onSubmit={handleSubmit(onFormSubmit)}>
        <DialogContent>
          <Grid container spacing={3}>
            {/* Left side - Vertical Stepper */}
            <Grid item xs={12} md={4}>
              <Stepper activeStep={activeStep} orientation="vertical" connector={null}>
                {steps.map((step, index) => {
                  const isCompleted = index < activeStep;
                  const isActive = index === activeStep;
                  return (
                    <Step key={step.label} completed={isCompleted}>
                      <StepLabel
                        StepIconComponent={() => (
                          <Radio
                            checked={isActive || isCompleted}
                            sx={{
                              p: 0,
                              color: isCompleted ? 'primary.main' : 'inherit',
                            }}
                            readOnly
                          />
                        )}
                      >
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Iconify
                            icon={step.icon}
                            width={24}
                            height={24}
                            sx={{
                              color: isActive || isCompleted ? 'primary.main' : 'text.secondary',
                            }}
                          />
                          <Typography
                            variant="subtitle1"
                            sx={{
                              color: isActive || isCompleted ? 'primary.main' : 'text.primary',
                              fontWeight: isActive || isCompleted ? 600 : 400,
                            }}
                          >
                            {step.label}
                          </Typography>
                        </Stack>
                      </StepLabel>
                      <StepContent>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, ml: 3 }}>
                          {step.description}
                        </Typography>
                      </StepContent>
                    </Step>
                  );
                })}
              </Stepper>
            </Grid>

            {/* Right side - Form Fields */}
            <Grid item xs={12} md={8}>
              <Box sx={{ p: 1 }}>{steps[activeStep].fields}</Box>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Stack
            direction="row"
            spacing={2}
            sx={{ width: '100%', justifyContent: 'flex-end', pb: 2, pr: 2 }}
          >
            <AppButton
              label="Cancel"
              variant="outlined"
              color="inherit"
              onClick={onClose}
              fullWidth={false}
            />

            {activeStep > 0 && (
              <AppButton label="Back" variant="outlined" onClick={handleBack} fullWidth={false} />
            )}

            {activeStep === 0 ? (
              <AppButton label="Next" variant="contained" onClick={handleNext} fullWidth={false} />
            ) : (
              <AppButton
                fullWidth={false}
                label={agent ? 'Update' : 'Create'}
                type="submit"
                variant="contained"
                isLoading={isSubmitting}
              />
            )}
          </Stack>
        </DialogActions>
      </Form>
    </Dialog>
  );
}
