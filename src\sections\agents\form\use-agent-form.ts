import { useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useTheme } from '@mui/material';

// Form validation schema
const agentSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  systemMessage: z.string().min(1, 'System message is required'),
  email: z.string().email('Email must be a valid email address').min(1, 'Email is required'),
  category: z.string().min(1, 'Category is required'),
  status: z.string().min(1, 'Status is required'),
  tools: z.array(z.string()).default([]),
});

// Form values type
export type AgentFormValues = z.infer<typeof agentSchema>;

// Agent type
export interface Agent {
  id: string;
  name: string;
  description: string;
  systemMessage?: string;
  email: string;
  avatarUrl: string;
  category: string;
  status: string;
  tools?: string[];
}

// Available tools
export const availableTools = [
  { id: 'gmail', name: 'Gmail', icon: 'mdi:gmail', color: '#D44638' },
  { id: 'facebook', name: 'Facebook', icon: 'mdi:facebook', color: '#1877F2' },
  { id: 'twitter', name: 'Twitter/X', icon: 'mdi:twitter', color: '#1DA1F2' },
  { id: 'instagram', name: 'Instagram', icon: 'mdi:instagram', color: '#E4405F' },
  { id: 'linkedin', name: 'LinkedIn', icon: 'mdi:linkedin', color: '#0A66C2' },
  { id: 'slack', name: 'Slack', icon: 'mdi:slack', color: '#4A154B' },
  { id: 'github', name: 'GitHub', icon: 'mdi:github', color: '#181717' },
  { id: 'dropbox', name: 'Dropbox', icon: 'mdi:dropbox', color: '#0061FF' },
];

// Category options
export const CATEGORY_OPTIONS = [
  { value: 'Sales', label: 'Sales' },
  { value: 'Marketing', label: 'Marketing' },
  { value: 'Support', label: 'Support' },
  { value: 'HR', label: 'HR' },
  { value: 'IT', label: 'IT' },
];

// Status options
export const STATUS_OPTIONS = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
];

// Steps definition
export const AGENT_FORM_STEPS = [
  {
    label: 'Agent Info',
    icon: 'mdi:account-tie',
    description: 'Enter the basic information about the agent',
  },
  {
    label: 'Agent Tools',
    icon: 'mdi:tools',
    description: 'Select the tools this agent can use',
  },
];

interface UseAgentFormProps {
  agent: Agent | null;
  onSubmit: (data: AgentFormValues) => void;
}

export function useAgentForm({ agent, onSubmit }: UseAgentFormProps) {
  // Make these constants available to the component
  const agentTools = availableTools;
  const categoryOptions = CATEGORY_OPTIONS;
  const statusOptions = STATUS_OPTIONS;
  const theme = useTheme();

  // State for the active step
  const [activeStep, setActiveStep] = useState(0);

  // State for search query
  const [searchQuery, setSearchQuery] = useState('');

  // Handle search change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  }, []);

  // Filter available tools based on search query
  const filteredTools = searchQuery
    ? agentTools.filter((tool) =>
        tool.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : agentTools;

  // Initialize form with default values or agent data for editing
  const methods = useForm<AgentFormValues>({
    mode: 'onChange',
    resolver: zodResolver(agentSchema),
    defaultValues: agent
      ? {
          name: agent.name,
          description: agent.description,
          systemMessage: agent.systemMessage || '',
          email: agent.email,
          category: agent.category,
          status: agent.status,
          tools: agent.tools || [],
        }
      : {
          name: '',
          description: '',
          systemMessage: '',
          email: '',
          category: '',
          status: 'active',
          tools: [],
        },
  });

  const {
    handleSubmit,
    trigger,
    setValue,
    watch,
    formState: { isSubmitting },
  } = methods;

  // Watch for changes in the tools array
  const selectedTools = watch('tools');

  // Handle next step
  const handleNext = async () => {
    // Validate fields in the current step
    const fieldsToValidate = ['name', 'description', 'systemMessage'];
    const isStepValid = await trigger(fieldsToValidate as any);

    if (isStepValid) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Handle tool selection
  const handleToolToggle = (toolId: string) => {
    const currentTools = [...selectedTools];
    const toolIndex = currentTools.indexOf(toolId);

    if (toolIndex === -1) {
      // Add the tool
      currentTools.push(toolId);
    } else {
      // Remove the tool
      currentTools.splice(toolIndex, 1);
    }

    setValue('tools', currentTools);
  };

  // Handle form submission
  const onFormSubmit = async (data: AgentFormValues) => {
    onSubmit(data);
  };

  return {
    theme,
    activeStep,
    methods,
    selectedTools,
    isSubmitting,
    handleNext,
    handleBack,
    handleToolToggle,
    onFormSubmit,
    handleSubmit,
    searchQuery,
    handleSearchChange,
    availableTools: filteredTools,
    CATEGORY_OPTIONS: categoryOptions,
    STATUS_OPTIONS: statusOptions,
  };
}
