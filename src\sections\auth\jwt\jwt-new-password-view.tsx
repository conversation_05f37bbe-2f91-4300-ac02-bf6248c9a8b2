import { z as zod } from 'zod';
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import InputAdornment from '@mui/material/InputAdornment';

import { AppButton } from 'src/components/common';

import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';

import { useBoolean } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

import { AuthHeader } from './components/auth-header';

// ----------------------------------------------------------------------

export const NewPasswordSchema = zod
  .object({
    password: zod
      .string()
      .min(1, { message: 'Password is required!' })
      .min(8, { message: 'Password must be at least 8 characters!' })
      .regex(/[A-Z]/, { message: 'Password must include at least one uppercase letter!' })
      .regex(/[a-z]/, { message: 'Password must include at least one lowercase letter!' })
      .regex(/[0-9]/, { message: 'Password must include at least one number!' })
      .regex(/[^A-Za-z0-9\s]/, { message: 'Password must include at least one special character!' })
      .refine((value) => !value.startsWith(' ') && !value.endsWith(' '), {
        message: 'Password cannot have spaces at the beginning or end!',
      }),
    confirmPassword: zod.string().min(1, { message: 'Confirm password is required!' }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords must match!',
    path: ['confirmPassword'],
  });

export type NewPasswordSchemaType = zod.infer<typeof NewPasswordSchema>;

// ----------------------------------------------------------------------

export function JwtNewPasswordView() {
  const { t } = useTranslation();
  const router = useRouter();
  const [errorMsg, setErrorMsg] = useState('');
  const [successMsg, setSuccessMsg] = useState('');

  const password = useBoolean();
  const confirmPassword = useBoolean();

  // Validation states
  const [validations, setValidations] = useState({
    minLength: false,
    hasUpperLower: false,
    hasNumber: false,
    hasSpecial: false,
    noSpaces: false,
    passwordsMatch: false,
  });

  const methods = useForm<NewPasswordSchemaType>({
    resolver: zodResolver(NewPasswordSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    mode: 'onChange',
  });

  // Function to check password validations
  const checkPasswordValidation = (password: string, confirmPassword: string) => {
    setValidations({
      minLength: password.length >= 8,
      hasUpperLower: /[A-Z]/.test(password) && /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password),
      hasSpecial: /[^A-Za-z0-9\s]/.test(password),
      noSpaces: !password.startsWith(' ') && !password.endsWith(' '),
      passwordsMatch: password === confirmPassword && password !== '',
    });
  };

  // Watch for password and confirmPassword changes
  const watchPassword = methods.watch('password');
  const watchConfirmPassword = methods.watch('confirmPassword');

  // Update validations when passwords change
  useEffect(() => {
    checkPasswordValidation(watchPassword || '', watchConfirmPassword || '');
  }, [watchPassword, watchConfirmPassword]);

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (_data) => {
    try {
      // Here you would call your API to change the password
      // Password would be sent to backend
      // console.log('New password:', data.password);

      // Show success message
      setSuccessMsg('Password changed successfully!');
      setErrorMsg('');

      // Redirect to sign in page after a short delay
      setTimeout(() => {
        router.push(paths.auth.jwt.signIn);
      }, 2000);
    } catch (error) {
      console.error(error);
      setErrorMsg(error instanceof Error ? error.message : 'Something went wrong');
      setSuccessMsg('');
    }
  });

  return (
    <>
      <AuthHeader />
      <Box
        sx={{
          padding: 2,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%',
          maxWidth: '600px',
          margin: '0 auto'
        }}
      >
        <Box sx={{ width: 40, height: 40, mb: 4 }} />
        <Typography variant="h1" textAlign="center" paragraph sx={{ mb: 3 }}>
          {t('auth.newPasswordView.title')}
        </Typography>

        <Typography
          variant="body1"
          textAlign="center"
          sx={{ color: 'text.secondary', mb: 4, lineHeight: 1.5 }}
        >
          {t('auth.newPasswordView.subtitle')}
        </Typography>

        {!!successMsg && (
          <Alert severity="success" sx={{ mb: 4 }}>
            {successMsg}
          </Alert>
        )}

        {!!errorMsg && (
          <Alert severity="error" sx={{ mb: 4 }}>
            {errorMsg}
          </Alert>
        )}

        <Form methods={methods} onSubmit={onSubmit}>
          <Stack spacing={4} sx={{ width: '100%', gap: '20px' }}>
            <Field.Text
              name="password"
              label={t('auth.newPasswordView.newPassword')}
              type={password.value ? 'text' : 'password'}
              placeholder={t('auth.newPasswordView.newPasswordPlaceholder')}
              fullWidth
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton onClick={password.onToggle} edge="end">
                      <Iconify icon={password.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'} />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <Box sx={{ mb: 2 }}>
              <Stack spacing={1}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify
                    icon={
                      validations.minLength ? 'eva:checkmark-circle-fill' : 'eva:close-circle-fill'
                    }
                    color={validations.minLength ? 'success.main' : 'error.main'}
                    width={16}
                  />
                  <Typography variant="body2">
                    {t('auth.newPasswordView.passwordRequirements.minLength')}
                  </Typography>
                </Stack>

                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify
                    icon={
                      validations.hasUpperLower
                        ? 'eva:checkmark-circle-fill'
                        : 'eva:close-circle-fill'
                    }
                    color={validations.hasUpperLower ? 'success.main' : 'error.main'}
                    width={16}
                  />
                  <Typography variant="body2">
                    {t('auth.newPasswordView.passwordRequirements.hasUpperLower')}
                  </Typography>
                </Stack>

                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify
                    icon={
                      validations.hasSpecial ? 'eva:checkmark-circle-fill' : 'eva:close-circle-fill'
                    }
                    color={validations.hasSpecial ? 'success.main' : 'error.main'}
                    width={16}
                  />
                  <Typography variant="body2">
                    {t('auth.newPasswordView.passwordRequirements.hasSpecial')}
                  </Typography>
                </Stack>

                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify
                    icon={
                      validations.noSpaces ? 'eva:checkmark-circle-fill' : 'eva:close-circle-fill'
                    }
                    color={validations.noSpaces ? 'success.main' : 'error.main'}
                    width={16}
                  />
                  <Typography variant="body2">
                    {t('auth.newPasswordView.passwordRequirements.noSpaces')}
                  </Typography>
                </Stack>
              </Stack>
            </Box>

            <Field.Text
              name="confirmPassword"
              label={t('auth.newPasswordView.confirmPassword')}
              type={confirmPassword.value ? 'text' : 'password'}
              placeholder={t('auth.newPasswordView.confirmPasswordPlaceholder')}
              fullWidth
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton onClick={confirmPassword.onToggle} edge="end">
                      <Iconify
                        icon={confirmPassword.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'}
                      />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <AppButton
              label={t('auth.newPasswordView.changePassword')}
              fullWidth
              color="primary"
              size="large"
              type="submit"
              variant="contained"
              isLoading={isSubmitting}
            />
          </Stack>
        </Form>

        <Stack direction="row" spacing={1} justifyContent="center" alignItems="center" sx={{ mt: 4, mb: 2, position: 'relative' }}>
          <Link variant="body2" color="text.secondary" href="#">
            {t('auth.newPasswordView.footerLinks.privacyPolicy')}
          </Link>
          <Typography color="text.disabled" sx={{ fontSize: '30px' }}>
            •
          </Typography>
          <Link variant="body2" color="text.secondary" href="#">
            {t('auth.newPasswordView.footerLinks.termsOfUse')}
          </Link>
          <Typography color="text.disabled" sx={{ fontSize: '30px' }}>
            •
          </Typography>
          <Link variant="body2" color="text.secondary" href="#">
            {t('auth.newPasswordView.footerLinks.dmca')}
          </Link>
        </Stack>
      </Box>
    </>
  );
}
