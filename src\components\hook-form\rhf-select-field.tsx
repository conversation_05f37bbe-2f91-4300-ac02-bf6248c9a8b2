import { Controller, useFormContext } from 'react-hook-form';
import TextField from '@mui/material/TextField';
import type { StandardTextFieldProps } from '@mui/material/TextField';

// ----------------------------------------------------------------------

type Props = Omit<StandardTextFieldProps, 'select'> & {
  name: string;
  children: React.ReactNode;
};

export function RHFSelectField({ name, label, helperText, children, ...other }: Props) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <TextField
          {...field}
          select
          fullWidth
          label={label}
          error={!!error}
          helperText={error?.message ?? helperText}
          {...other}
        >
          {children}
        </TextField>
      )}
    />
  );
}
