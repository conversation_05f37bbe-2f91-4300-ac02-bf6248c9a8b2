import { useState, useCallback } from 'react';
import { useBoolean } from 'src/hooks/use-boolean';

// Team member type
export interface TeamMember {
  id: string;
  name: string;
  avatarUrl: string;
  role: string;
}

// Team data type
export interface TeamData {
  id: string;
  name: string;
  description: string;
  type: string;
  members: TeamMember[];
  createdAt: Date;
}

// Form values type
export interface TeamFormValues {
  name: string;
  description: string;
  type: string;
  members: string[]; // Array of member IDs
  // New fields for additional steps
  resources?: string;
  aiModel?: string;
  instructions?: string;
}

export function useTeamsView(initialTeams: TeamData[]) {
  const [teams, setTeams] = useState<TeamData[]>(initialTeams);
  const [selectedTeam, setSelectedTeam] = useState<TeamData | null>(null);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [selectedId, setSelectedId] = useState<string | null>(null);

  // Form dialog
  const formDialog = useBoolean();

  // Handle opening the form dialog for adding a new team
  const handleAddTeam = useCallback(() => {
    setSelectedTeam(null);
    formDialog.onTrue();
  }, [formDialog]);

  // Handle opening the form dialog for editing a team
  const handleEditTeam = useCallback((id: string) => {
    const team = teams.find((t) => t.id === id);
    if (team) {
      setSelectedTeam(team);
      formDialog.onTrue();
    }
  }, [teams, formDialog]);

  // Handle viewing a team
  const handleViewTeam = useCallback((id: string) => {
    // Implement view functionality here
    console.log(`Viewing team with ID: ${id}`);
  }, []);

  // Handle opening the confirm dialog for deleting a team
  const handleOpenConfirmDialog = useCallback((id: string) => {
    setSelectedId(id);
    setOpenConfirmDialog(true);
  }, []);

  // Handle closing the confirm dialog
  const handleCloseConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(false);
  }, []);

  // Handle deleting a team
  const handleDeleteTeam = useCallback(() => {
    if (selectedId) {
      setTeams((prev) => prev.filter((team) => team.id !== selectedId));
      setOpenConfirmDialog(false);
      setSelectedId(null);
    }
  }, [selectedId]);

  // Handle form submission
  const handleFormSubmit = useCallback(
    (data: TeamFormValues) => {
      if (selectedTeam) {
        // Edit existing team
        setTeams((prev) =>
          prev.map((team) => {
            if (team.id === selectedTeam.id) {
              // Find members from the selected member IDs
              const updatedMembers = selectedTeam.members.filter((member) =>
                data.members.includes(member.id)
              );

              return {
                ...team,
                ...data,
                members: updatedMembers,
              };
            }
            return team;
          })
        );
      } else {
        // Add new team
        // In a real app, you would fetch the actual member data based on IDs
        // For now, we'll create dummy members
        const dummyMembers: TeamMember[] = data.members.map((id, index) => ({
          id,
          name: `Team Member ${index + 1}`,
          avatarUrl: `/assets/images/avatar/avatar_${(index % 5) + 1}.jpg`,
          role: 'Member',
        }));

        const newTeam: TeamData = {
          id: String(teams.length + 1),
          name: data.name,
          description: data.description,
          type: data.type,
          members: dummyMembers,
          createdAt: new Date(),
        };

        setTeams((prev) => [...prev, newTeam]);
      }
      formDialog.onFalse();
    },
    [teams, formDialog, selectedTeam]
  );

  return {
    // State
    teams,
    selectedTeam,
    openConfirmDialog,
    selectedId,
    formDialog,

    // Handlers
    handleAddTeam,
    handleEditTeam,
    handleViewTeam,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleDeleteTeam,
    handleFormSubmit,
  };
}
