import { useState } from 'react';
import { Card, Stack, Grid, Typography, InputAdornment, IconButton } from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { paths } from 'src/routes/paths';
import { Field } from 'src/components/hook-form/fields';
import { Form } from 'src/components/hook-form/form-provider';
import { AppButton } from 'src/components/common';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { ManagerData } from '../view/use-managers-view';
import { useManagerForm } from './use-manager-form';

// ----------------------------------------------------------------------

// Component props
interface ManagerFormProps {
  manager?: ManagerData | null;
  onSubmit: (data: any, id?: string) => void;
}

export default function ManagerForm({ manager, onSubmit }: ManagerFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Use the custom hook for form logic
  const { methods, isSubmitting, onFormSubmit, handleSubmit, handleCancel } = useManagerForm({
    manager,
    onSubmit,
  });

  return (
    <>
      <CustomBreadcrumbs
        heading={manager ? 'Edit Manager' : 'Managers'}
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'Managers', href: paths.dashboard.managers.root },
          { name: manager ? 'Edit' : 'Create' },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Form methods={methods} onSubmit={handleSubmit(onFormSubmit)}>
        <Card  sx={{ border:'none',borderRadius:'0px',backgroundColor:'transparent', boxShadow:'none', mt:'10px',py:3 }}>
         

          <Grid container spacing={3} >
            <Grid item xs={12} md={6}>
              <Field.Text name="firstName" label="First Name" />
            </Grid>

            <Grid item xs={12} md={6}>
              <Field.Text name="lastName" label="Last Name" />
            </Grid>

            <Grid item xs={12} md={6}>
              <Field.Text name="username" label="Username" />
            </Grid>

            <Grid item xs={12} md={6}>
              <Field.Text name="email" label="Email" />
            </Grid>

            <Grid item xs={12} md={6}>
              <Field.Text
                name="password"
                label="Password"
                type={showPassword ? 'text' : 'password'}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton onClick={() => setShowPassword(!showPassword)} edge="end">
                        <Iconify icon={showPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                helperText={
                  manager
                    ? 'Leave blank to keep current password'
                    : 'Password must be at least 8 characters with uppercase, lowercase, and number'
                }
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <Field.Text
                name="confirmPassword"
                label="Confirm Password"
                type={showConfirmPassword ? 'text' : 'password'}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        edge="end"
                      >
                        <Iconify icon={showConfirmPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                helperText={manager ? 'Leave blank to keep current password' : ''}
              />
            </Grid>
          </Grid>

          <Stack direction="row" justifyContent="flex-end" spacing={2} sx={{ mt: 3 }}>
            <AppButton
              label={manager ? 'Update' : 'Create'}
              type="submit"
              variant="contained"
              sx={{height:'48px'}}
              isLoading={isSubmitting}
            />
            <AppButton label="Cancel" variant="outlined" color="inherit" onClick={handleCancel} />
          </Stack>
        </Card>
      </Form>
    </>
  );
}
