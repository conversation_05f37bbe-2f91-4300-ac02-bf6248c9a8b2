# ------------------------------------- build ------------------------------------------

# Directly deploy to Cloudflare Pages, which saves time (and money) instead of dealing with artifacts

"build workforces-frontend without deploy":
  stage: build
  needs: []
  image: node:lts
  cache:
    key:
      prefix: workforces-frontend
      files:
        - yarn.lock
    paths:
      - .yarn/cache
      - .yarn/install-state.gz
      - ./node_modules
  variables:
    APP_NAME: workforces-frontend
    APP_ENV: dev
  environment:
    name: ${APP_NAME}-${APP_ENV}
  script:
    # - cp src/configuration.${APP_ENV}.ts src/configuration.ts
    - npm pkg set installConfig.hoistingLimits=workspaces
    - yarn install
    - REACT_APP_SERVICE_ENV="$APP_ENV" REACT_APP_SERVICE_VERSION="${CI_COMMIT_BRANCH}_${CI_COMMIT_SHORT_SHA}" yarn build
    - '[ -f "dist/index.html" ] || { ls -F dist/; echo "Error: Build was invalid. dist/index.html must exist after build!"; exit 1; }'
  # artifacts:
  #   paths:
  #     # - package.json
  #     # - .pnp.*
  #     # - .yarnrc.yml
  #     # - .yarn/cache/
  #     # - .yarn/unplugged/
  #     # - node_modules/
  #     - apps/dashboard/build/
  #     # - apps/convey/dist/
  #     # - apps/dashboard/node_modules/
  #     # - next-i18next.config.js
  #     # - next.config.js
  #     # - public/
  #     # - .next/
  #   expire_in: 1 week
  # environment:
  #   name: review/$CI_COMMIT_REF_SLUG
  rules:
    - if: '$CI_COMMIT_BRANCH == "main" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'
      # changes:
      #   - 'apps/dashboard/**/*'
    # - if: '$CI_COMMIT_BRANCH == "stack/dev" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'
    #   # changes:
    #   #   - 'apps/dashboard/**/*'
    # # You can manually trigger the pipeline with APP_NAMES=workforces-frontend
    # - if: '$CI_COMMIT_BRANCH == "stack/dev" && $APP_NAMES =~ /\bworkforces-frontend\b/'

"build then deploy workforces-frontend to dev":
  stage: build
  needs: ["prepare version"]
  image: node:lts
  cache:
    key:
      prefix: workforces-frontend
      files:
        - yarn.lock
    paths:
      - .yarn/cache
      - .yarn/install-state.gz
      - ./node_modules
  variables:
    APP_NAME: workforces-frontend
    APP_ENV: dev
  environment:
    name: ${APP_NAME}-${APP_ENV}
    url: https://${APP_ENV}.workforces.ai/
  script:
    # - cp src/configuration.${APP_ENV}.ts src/configuration.ts
    - npm pkg set installConfig.hoistingLimits=workspaces
    - yarn install
    - REACT_APP_SERVICE_ENV="$APP_ENV" REACT_APP_SERVICE_VERSION="$APP_VERSION" yarn build
    - '[ -f "dist/index.html" ] || { ls -F dist/; echo "Error: Build was invalid. dist/index.html must exist after build!"; exit 1; }'
    # Cloudflare Pages insists on `main` branch for some reason
    - yarn wrangler pages deploy dist/ --project-name="${APP_NAME}-${APP_ENV}" --branch=main --commit-hash="$CI_COMMIT_SHORT_SHA" --commit-message="${CI_COMMIT_DESCRIPTION}" --commit-dirty=true
  # artifacts:
  #   paths:
  #     # - package.json
  #     # - .pnp.*
  #     # - .yarnrc.yml
  #     # - .yarn/cache/
  #     # - .yarn/unplugged/
  #     # - node_modules/
  #     - apps/chrome-dashboard/build/
  #     # - apps/convey/dist/
  #     # - apps/chrome-dashboard/node_modules/
  #     # - next-i18next.config.js
  #     # - next.config.js
  #     # - public/
  #     # - .next/
  #   expire_in: 1 week
  # environment:
  #   name: review/$CI_COMMIT_REF_SLUG
  rules:
    # - if: '$CI_COMMIT_BRANCH == "main" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'
    #   # changes:
    #   #   - 'apps/chrome-dashboard/**/*'
    - if: '$CI_COMMIT_BRANCH == "stack/dev" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'
      # changes:
      #   - 'apps/chrome-dashboard/**/*'

"build then deploy workforces-frontend to staging":
  stage: build
  needs: ["prepare version"]
  image: node:lts
  cache:
    key:
      prefix: workforces-frontend
      files:
        - yarn.lock
    paths:
      - .yarn/cache
      - .yarn/install-state.gz
      - ./node_modules
  variables:
    APP_NAME: workforces-frontend
    APP_ENV: staging
  environment:
    name: ${APP_NAME}-${APP_ENV}
    url: https://${APP_ENV}.workforces.ai//
  script:
    # - cp src/configuration.${APP_ENV}.ts src/configuration.ts
    - npm pkg set installConfig.hoistingLimits=workspaces
    - yarn install
    - REACT_APP_SERVICE_ENV="$APP_ENV" REACT_APP_SERVICE_VERSION="$APP_VERSION" yarn build
    - '[ -f "dist/index.html" ] || { ls -F dist/; echo "Error: Build was invalid. dist/index.html must exist after build!"; exit 1; }'
    # Cloudflare Pages insists on `main` branch for some reason
    - yarn wrangler pages deploy dist/ --project-name="${APP_NAME}-${APP_ENV}" --branch=main --commit-hash="$CI_COMMIT_SHORT_SHA" --commit-message="${CI_COMMIT_DESCRIPTION}" --commit-dirty=true
  # artifacts:
  #   paths:
  #     # - package.json
  #     # - .pnp.*
  #     # - .yarnrc.yml
  #     # - .yarn/cache/
  #     # - .yarn/unplugged/
  #     # - node_modules/
  #     - apps/chrome-dashboard/build/
  #     # - apps/convey/dist/
  #     # - apps/chrome-dashboard/node_modules/
  #     # - next-i18next.config.js
  #     # - next.config.js
  #     # - public/
  #     # - .next/
  #   expire_in: 1 week
  # environment:
  #   name: review/$CI_COMMIT_REF_SLUG
  rules:
    - if: '$CI_COMMIT_BRANCH == "stack/staging" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'
      # changes:
      #   - 'apps/chrome-dashboard/**/*'
    # You can manually trigger the pipeline with APP_NAMES=workforces-frontend
    - if: '$CI_COMMIT_BRANCH == "stack/staging" && $APP_NAMES =~ /\bworkforces-frontend\b/'

"build then deploy workforces-frontend to prod":
  stage: build
  needs: ["prepare version"]
  image: node:lts
  cache:
    key:
      prefix: workforces-frontend
      files:
        - yarn.lock
    paths:
      - .yarn/cache
      - .yarn/install-state.gz
      - ./node_modules
  variables:
    APP_NAME: workforces-frontend
    APP_ENV: prod
  environment:
    name: ${APP_NAME}-${APP_ENV}
    url: https://${APP_ENV}.workforces.ai/
  script:
    # - cp src/configuration.${APP_ENV}.ts src/configuration.ts
    - npm pkg set installConfig.hoistingLimits=workspaces
    - yarn install
    - REACT_APP_SERVICE_ENV="$APP_ENV" REACT_APP_SERVICE_VERSION="$APP_VERSION" yarn build
    - '[ -f "dist/index.html" ] || { ls -F dist/; echo "Error: Build was invalid. dist/index.html must exist after build!"; exit 1; }'
    # Cloudflare Pages insists on `main` branch for some reason
    - yarn wrangler pages deploy dist/ --project-name="${APP_NAME}-${APP_ENV}" --branch=main --commit-hash="$CI_COMMIT_SHORT_SHA" --commit-message="${CI_COMMIT_DESCRIPTION}" --commit-dirty=true
  # artifacts:
  #   paths:
  #     # - package.json
  #     # - .pnp.*
  #     # - .yarnrc.yml
  #     # - .yarn/cache/
  #     # - .yarn/unplugged/
  #     # - node_modules/
  #     - apps/chrome-dashboard/build/
  #     # - apps/convey/dist/
  #     # - apps/chrome-dashboard/node_modules/
  #     # - next-i18next.config.js
  #     # - next.config.js
  #     # - public/
  #     # - .next/
  #   expire_in: 1 week
  # environment:
  #   name: review/$CI_COMMIT_REF_SLUG
  rules:
    - if: '$CI_COMMIT_BRANCH == "stack/prod" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'
      # changes:
      #   - 'apps/chrome-dashboard/**/*'
    # You can manually trigger the pipeline with APP_NAMES=workforces-frontend
    - if: '$CI_COMMIT_BRANCH == "stack/prod" && $APP_NAMES =~ /\bworkforces-frontend\b/'
