import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Stack } from '@mui/material';
import { AppButton } from 'src/components/common';

import { Form } from 'src/components/hook-form/form-provider';
import { Field } from 'src/components/hook-form/fields';

import { categorySchema, CategoryFormValues } from './category-schema';
import IconSelector from './icon-selector';
import ColorSelector from './color-selector';

interface CategoryFormProps {
  onSubmit: (data: CategoryFormValues) => void;
  onCancel: () => void;
  defaultValues?: Partial<CategoryFormValues>;
}

export default function CategoryForm({ onSubmit, onCancel, defaultValues }: CategoryFormProps) {
  const methods = useForm<CategoryFormValues>({
    mode: 'onChange',
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: '',
      description: '',
      icon: '',
      colorType: 'primary',
      customColor: '#FF5733',
      ...defaultValues,
    },
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = methods;

  const renderForm = (
    <Stack spacing={3} p={2}>
      <Stack direction="row" alignItems="center" spacing={2}>
        <IconSelector />
        <Field.Text
          name="name"
          label="Category Name"
          InputLabelProps={{ shrink: true }}
          sx={{ flexGrow: 1 }}
        />
      </Stack>

      <Field.Text
        name="description"
        label="Description"
        multiline
        rows={6}
        InputLabelProps={{ shrink: true }}
      />

      <ColorSelector
        control={control}
        error={!!errors.colorType}
        helperText={errors.colorType?.message}
      />

      <Stack direction="row" spacing={2} justifyContent="center">
        <AppButton
          type="submit"
          variant="contained"
          color="primary"
          
          isLoading={isSubmitting}
          label={defaultValues ? 'Update' : 'Add'}
          sx={{height:'48px'}}
        />
        <AppButton color="inherit" variant="outlined" onClick={onCancel} label="Cancel" />
      </Stack>
    </Stack>
  );

  return (
    <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
      {renderForm}
    </Form>
  );
}
