# You can override the included template(s) by including variable overrides
# SAST customization: https://docs.gitlab.com/ee/user/application_security/sast/#customizing-the-sast-settings
# Secret Detection customization: https://docs.gitlab.com/ee/user/application_security/secret_detection/#customizing-settings
# Dependency Scanning customization: https://docs.gitlab.com/ee/user/application_security/dependency_scanning/#customizing-the-dependency-scanning-settings
# Note that environment variables can be set in several places
# See https://docs.gitlab.com/ee/ci/variables/#cicd-variable-precedence

#
stages:
  - pre
  - pre-publish
  - build
  - test
  - publish
  - post-publish
  - deploy

image: node:lts

# Cache modules in between jobs
cache:
  key: $CI_COMMIT_REF_SLUG
  paths:
    - $HOME/.npm/
    - node_modules/
    - .yarn/cache/

# # Semgrep - https://www.notion.so/hendyirawan/How-To-Add-Semgrep-To-A-GitLab-Project-0c98345d99c24d64876eb53ae6ed27e9
# # Hendy's note: disable Semgrep temporarily due to js dangerouslyset bug
# semgrep:
#   image: returntocorp/semgrep-agent:v1
#   script: semgrep-agent
#   variables: {}
#   rules:
#     - if: $CI_MERGE_REQUEST_IID
#     - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# sonarqube-check:
#   stage: build
#   image:
#     name: sonarsource/sonar-scanner-cli:latest
#     entrypoint: ['']
#   variables:
#     SONAR_USER_HOME: '${CI_PROJECT_DIR}/.sonar' # Defines the location of the analysis task cache
#     GIT_DEPTH: '0' # Tells git to fetch all the branches of the project, required by the analysis task
#   cache:
#     key: '${CI_JOB_NAME}'
#     paths:
#       - .sonar/cache
#   script:
#     - sonar-scanner
#   allow_failure: true
#   only:
#     - main # or the name of your main branch

.dev_rules:
  rules:
    - if: '$CI_COMMIT_BRANCH == "stack/dev" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'

.staging_rules:
  rules:
    - if: '$CI_COMMIT_BRANCH == "stack/staging" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'

.prod_rules:
  rules:
    - if: '$CI_COMMIT_BRANCH == "stack/prod" && ($CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event")'

# ------------------------------------- pre-publish ------------------------------------------

"prepare version":
  stage: pre
  needs: []
  cache: []
  image: alpine
  script:
    - export APP_ENV=${CI_COMMIT_BRANCH/stack\//}
    - export APP_VERSION=$(date +%Y.%m.%d)-${APP_ENV}_${CI_COMMIT_SHORT_SHA}
    - echo "APP_ENV=$APP_ENV APP_VERSION=$APP_VERSION"
    - echo "APP_ENV=$APP_ENV" > build.env
    - echo "APP_VERSION=$APP_VERSION" >> build.env
    # transition
    - echo "SERVICE_VERSION=$APP_VERSION" >> build.env
    - echo "SERVICE_BUILD_ENV=$APP_ENV" >> build.env
  artifacts:
    reports:
      dotenv: build.env
    expire_in: 1 week
  # rules:
  #   - if: '$CI_COMMIT_BRANCH == "stack/dev"'
  #   - if: '$CI_COMMIT_BRANCH == "stack/staging"'
  #   - if: '$CI_COMMIT_BRANCH == "stack/prod"'

# -------------------------- libs and apps ------------------------------

include:
  - local: "/.gitlab-ci.frontend.yml"
  # - local: "/apps/pwa/.gitlab-ci.yml"
  # - local: "/apps/website/.gitlab-ci.yml"
