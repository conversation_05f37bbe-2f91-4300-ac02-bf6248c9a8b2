import { lazy, Suspense } from 'react';
import { Outlet } from 'react-router-dom';

import { AuthSplitLayout } from 'src/layouts/auth-split';

import { SplashScreen } from 'src/components/loading-screen';

import { GuestGuard } from 'src/auth/guard';

// ----------------------------------------------------------------------

/** **************************************
 * Jwt
 *************************************** */
const Jwt = {
  SignInPage: lazy(() => import('src/pages/auth/jwt/sign-in')),
  SignUpPage: lazy(() => import('src/pages/auth/jwt/sign-up')),
  ForgotPasswordPage: lazy(() => import('src/pages/auth/jwt/forgot-password')),
  NewPasswordPage: lazy(() => import('src/pages/auth/jwt/new-password')),
};

const authJwt = {
  path: 'jwt',
  children: [
    {
      path: 'sign-in',
      element: (
        <GuestGuard>
          <AuthSplitLayout
            isLoginPage
            section={{
              title: 'Welcome to Midad!',
              subtitle:
                'Access your personalized dashboard, track your workflows, and manage your tasks..',
            }}
          >
            <Jwt.SignInPage />
          </AuthSplitLayout>
        </GuestGuard>
      ),
    },
    {
      path: 'sign-up',
      element: (
        <GuestGuard>
          <AuthSplitLayout
          isRegisterPage
            section={{
              title: 'Join us and start automating your workflows!',
              subtitle:
                'Sign up today to gain access to our platform, manage automations, and unlock the power of seamless task automation.',
            }}
          >
            <Jwt.SignUpPage />
          </AuthSplitLayout>
        </GuestGuard>
      ),
    },
    {
      path: 'forgot-password',
      element: (
        <GuestGuard>
          <AuthSplitLayout
          isForgotPassword
            section={{
              title: 'Reset your password',
              subtitle:
                'Enter your email and we will send you a link to reset your password.',
            }}
          >
            <Jwt.ForgotPasswordPage />
          </AuthSplitLayout>
        </GuestGuard>
      ),
    },
    {
      path: 'new-password',
      element: (
        <GuestGuard>
          <AuthSplitLayout
          isForgotPassword
            section={{
              title: 'Set new password',
              subtitle:
                'Create a new password for your account.',
            }}
          >
            <Jwt.NewPasswordPage />
          </AuthSplitLayout>
        </GuestGuard>
      ),
    },
  ],
};

// ----------------------------------------------------------------------

export const authRoutes = [
  {
    path: 'auth',
    element: (
      <Suspense fallback={<SplashScreen />}>
        <Outlet />
      </Suspense>
    ),
    children: [authJwt],
  },
];
