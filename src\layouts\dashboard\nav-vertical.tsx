import { useCallback, useState } from 'react';
import type { Breakpoint } from '@mui/material/styles';
import type { NavSectionProps } from 'src/components/nav-section';
import { useTranslation } from 'react-i18next';

import Box from '@mui/material/Box';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import { varAlpha, hideScrollY } from 'src/theme/styles';

import { useAuthContext } from 'src/auth/hooks';
import { signOut } from 'src/auth/context/jwt';
import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { Logo } from 'src/components/logo';
import { Scrollbar } from 'src/components/scrollbar';
import { NavSectionMini, NavSectionVertical } from 'src/components/nav-section';
import { AppButton } from 'src/components/common';
import { Iconify } from 'src/components/iconify';
import ConfirmDialog from 'src/components/custom-dialog/confirm-dialog';

// import { NavUpgrade } from '../components/nav-upgrade';
import { NavToggleButton } from '../components/nav-toggle-button';

// ----------------------------------------------------------------------

export type NavVerticalProps = NavSectionProps & {
  isNavMini: boolean;
  layoutQuery: Breakpoint;
  onToggleNav: () => void;
  slots?: {
    topArea?: React.ReactNode;
    bottomArea?: React.ReactNode;
  };
};

export function NavVertical({
  sx,
  data,
  slots,
  isNavMini,
  layoutQuery,
  onToggleNav,
  ...other
}: NavVerticalProps) {
  const theme = useTheme();
  const router = useRouter();
  const { t } = useTranslation();

  // State for logout confirmation dialog
  const [openLogoutDialog, setOpenLogoutDialog] = useState(false);

  // Handle opening the logout confirmation dialog
  const handleOpenLogoutDialog = () => {
    setOpenLogoutDialog(true);
  };

  // Handle closing the logout confirmation dialog
  const handleCloseLogoutDialog = () => {
    setOpenLogoutDialog(false);
  };
  const { checkUserSession } = useAuthContext();

  // Handle logout after confirmation
  const handleLogout = useCallback(async () => {
    try {
      await signOut();
      await checkUserSession?.();

      setOpenLogoutDialog(false);
      router.push(paths.auth.jwt.signIn);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error during logout:', error);
    }
  }, [checkUserSession, router, setOpenLogoutDialog]);
  const renderNavVertical = (
    <>
      {slots?.topArea ?? (
        <Box sx={{ pl: 3.5, pt: 2.5, pb: 1 }}>
          <Logo />
        </Box>
      )}

      <Scrollbar fillContent>
        <NavSectionVertical data={data} sx={{ px: 2, flex: '1 1 auto' }} {...other} />

        <Box sx={{ p: 2.5, mt: 'auto' }}>
          <AppButton
            label={t('components.accountMenu.logout')}
            variant="outlined"
            color="primary"
            startIcon={<Iconify icon="eva:log-out-fill" />}
            onClick={handleOpenLogoutDialog}
          />
        </Box>

        {slots?.bottomArea}
      </Scrollbar>
    </>
  );

  const renderNavMini = (
    <>
      {slots?.topArea ?? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 2.5 }}>
          <Logo />
        </Box>
      )}

      <NavSectionMini
        data={data}
        sx={{ pb: 2, px: 0.5, ...hideScrollY, flex: '1 1 auto', overflowY: 'auto' }}
        {...other}
      />

      <Box sx={{ p: 1.5, mt: 'auto', display: 'flex', justifyContent: 'center' }}>
        <AppButton
          label=""
          variant="outlined"
          color="primary"
          startIcon={<Iconify icon="eva:log-out-fill" />}
          onClick={handleOpenLogoutDialog}
          sx={{ minWidth: 'auto', width: 40, height: 40, p: 0 }}
        />
      </Box>

      {slots?.bottomArea}
    </>
  );

  return (
    <Box
      sx={{
        top: 0,
        left: 0,
        height: 1,
        display: 'none',
        position: 'fixed',
        flexDirection: 'column',
        bgcolor: 'var(--layout-nav-bg)',
        zIndex: 'var(--layout-nav-zIndex)',
        width: isNavMini ? 'var(--layout-nav-mini-width)' : 'var(--layout-nav-vertical-width)',
        borderRight: `1px solid var(--layout-nav-border-color, ${varAlpha(theme.vars.palette.grey['500Channel'], 0.12)})`,
        transition: theme.transitions.create(['width'], {
          easing: 'var(--layout-transition-easing)',
          duration: 'var(--layout-transition-duration)',
        }),
        [theme.breakpoints.up(layoutQuery)]: {
          display: 'flex',
        },
        ...sx,
      }}
    >
      <NavToggleButton
        isNavMini={isNavMini}
        onClick={onToggleNav}
        sx={{
          display: 'none',
          [theme.breakpoints.up(layoutQuery)]: {
            display: 'inline-flex',
          },
        }}
      />
      {isNavMini ? renderNavMini : renderNavVertical}

      {/* Logout Confirmation Dialog */}
      <ConfirmDialog
        open={openLogoutDialog}
        onClose={handleCloseLogoutDialog}
        close={handleCloseLogoutDialog}
        title={
          <Typography variant="h3" textAlign="center">
            {t('components.dialogs.logout')}
          </Typography>
        }
        content={<Typography variant="body1">{t('components.dialogs.logoutConfirm')}</Typography>}
        icon={
          <Box sx={{ textAlign: 'center' }}>
            <Iconify
              icon="eva:alert-triangle-fill"
              sx={{ width: 64, height: 64, color: 'warning.main' }}
            />
          </Box>
        }
        action={
          <Stack direction="row" justifyContent="center" spacing={1} sx={{ width: '100%' }}>
            <AppButton
              sx={{ width: '45%' }}
              label={t('components.accountMenu.logout')}
              variant="contained"
              color="primary"
              onClick={handleLogout}
            />
            <AppButton
              sx={{ width: '45%' }}
              label={t('components.buttons.cancel')}
              variant="outlined"
              color="inherit"
              onClick={handleCloseLogoutDialog}
            />
          </Stack>
        }
      />
    </Box>
  );
}
