import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { ManagersView } from 'src/sections/managers/view/managers-view';

// ----------------------------------------------------------------------

export default function ManagersPage() {
  const { t } = useTranslation();

  return (
    <>
      <Helmet>
        <title>{`${t('pages.dashboard.title')}: ${t('pages.dashboard.managers')}`}</title>
      </Helmet>

      <ManagersView />
    </>
  );
}
