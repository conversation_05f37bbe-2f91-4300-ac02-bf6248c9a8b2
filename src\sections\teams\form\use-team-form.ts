import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useTheme } from '@mui/material';
import { TeamMember } from '../view/use-teams-view';

// Form validation schema
const teamSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  type: z.string().min(1, 'Type is required'),
  members: z.array(z.string()).min(1, 'At least one member is required'),
  // New fields for additional steps (optional for development)
  resources: z.string().optional(),
  aiModel: z.string().optional(),
  instructions: z.string().optional(),
});

// Form values type
export type TeamFormValues = z.infer<typeof teamSchema>;

// Team type
export interface Team {
  id: string;
  name: string;
  description: string;
  type: string;
  members: TeamMember[];
  createdAt: Date;
}

// Team type options
export const TEAM_TYPE_OPTIONS = [
  { value: 'Development', label: 'Development' },
  { value: 'Support', label: 'Support' },
  { value: 'Marketing', label: 'Marketing' },
  { value: 'Sales', label: 'Sales' },
  { value: 'Design', label: 'Design' },
  { value: 'HR', label: 'HR' },
];

// Steps definition
export const TEAM_FORM_STEPS = [
  {
    label: 'Team Info',
    icon: 'mdi:account-group',
    description: 'Enter the basic information about the team',
  },
  {
    label: 'Resources',
    icon: 'mdi:folder-multiple',
    description: 'Select resources for this team',
  },
  {
    label: 'AI Model',
    icon: 'mdi:robot',
    description: 'Configure AI model settings',
  },
  {
    label: 'Members',
    icon: 'mdi:account-multiple',
    description: 'Select the members for this team',
  },
  {
    label: 'Instructions',
    icon: 'mdi:file-document-edit',
    description: 'Add instructions for team members',
  }
];

interface UseTeamFormProps {
  team: Team | null;
  availableMembers: TeamMember[];
  onSubmit: (data: TeamFormValues) => void;
}

export function useTeamForm({ team, availableMembers, onSubmit }: UseTeamFormProps) {
  const theme = useTheme();

  // State for the active step
  const [activeStep, setActiveStep] = useState(0);

  // Development mode flag - set to true to skip validation
  const [devMode, setDevMode] = useState(true);

  // Initialize form with default values or team data for editing
  const methods = useForm<TeamFormValues>({
    mode: 'onChange',
    resolver: zodResolver(teamSchema),
    defaultValues: team
      ? {
          name: team.name,
          description: team.description,
          type: team.type,
          members: team.members.map(member => member.id),
          resources: '',
          aiModel: '',
          instructions: '',
        }
      : {
          name: '',
          description: '',
          type: '',
          members: [],
          resources: '',
          aiModel: '',
          instructions: '',
        },
  });

  const {
    handleSubmit,
    trigger,
    setValue,
    watch,
    formState: { isSubmitting },
  } = methods;

  // Watch for changes in the members array
  const selectedMemberIds = watch('members');

  // Handle next step
  const handleNext = async () => {
    // In dev mode, skip validation and proceed to next step
    if (devMode) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      return;
    }

    // Validate fields based on current step
    let fieldsToValidate: string[] = [];

    switch (activeStep) {
      case 0: // Team Info
        fieldsToValidate = ['name', 'description', 'type'];
        break;
      case 3: // Members
        fieldsToValidate = ['members'];
        break;
      default:
        // No validation for other steps
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        return;
    }

    const isStepValid = await trigger(fieldsToValidate as any);

    if (isStepValid) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Handle member selection (for Autocomplete component)
  const handleMemberChange = (newMembers: TeamMember[]) => {
    setValue('members', newMembers.map(member => member.id));
  };

  // Handle member selection by IDs (for MembersStep component)
  const handleMemberIdChange = (memberIds: string[]) => {
    setValue('members', memberIds);
  };

  // Handle form submission
  const onFormSubmit = async (data: TeamFormValues) => {
    onSubmit(data);
  };

  // Make these constants available to the component
  const teamTypeOptions = TEAM_TYPE_OPTIONS;
  const teamMembers = availableMembers;

  // Toggle development mode
  const toggleDevMode = () => {
    setDevMode((prev) => !prev);
  };

  return {
    theme,
    activeStep,
    methods,
    selectedMemberIds,
    isSubmitting,
    handleNext,
    handleBack,
    handleMemberChange,
    handleMemberIdChange,
    onFormSubmit,
    handleSubmit,
    TEAM_TYPE_OPTIONS: teamTypeOptions,
    availableMembers: teamMembers,
    devMode,
    toggleDevMode,
  };
}
