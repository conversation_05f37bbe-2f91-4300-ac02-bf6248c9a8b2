import { Ava<PERSON>, Stack, Typography } from '@mui/material';
import { useState, useCallback, useMemo } from 'react';
import { Label } from 'src/components/label';
import LongMenu from 'src/components/long-menu';
import { AppTablePropsType } from 'src/components/table';
import useTable from 'src/components/table/use-table';
import { useBoolean } from 'src/hooks/use-boolean';

// Agent data type
export interface AgentData {
  id: string;
  name: string;
  description: string;
  email: string;
  avatarUrl: string;
  category: string;
  status: string;
}

// Form values type
export interface AgentFormValues {
  name: string;
  description: string;
  email: string;
  category: string;
  status: string;
}

// Status tab options
export const STATUS_TAB_OPTIONS = [
  { value: 'all', label: 'All' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
];

// Category options will be dynamically generated with counts

export function useAgentsView(initialAgents: AgentData[]) {
  const [agents, setAgents] = useState<AgentData[]>(initialAgents);
  const [selectedAgent, setSelectedAgent] = useState<AgentData | null>(null);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [menuPosition, setMenuPosition] = useState<null | { top: number; left: number }>(null);
  const [selectedId, setSelectedId] = useState<string | null>(null);

  // Filtering states
  const [statusTab, setStatusTab] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Form dialog
  const formDialog = useBoolean();

  // Menu open state
  const openMenu = Boolean(menuPosition);

  // Table settings
  const table = useTable({
    defaultOrderBy: 'name',
  });

  // Handle opening the form dialog for adding a new agent
  const handleAddAgent = useCallback(() => {
    setSelectedAgent(null);
    formDialog.onTrue();
  }, [formDialog]);

  // Handle opening the form dialog for editing an agent
  const handleEditAgent = useCallback(
    (id: string) => {
      const agent = agents.find((a) => a.id === id);
      if (agent) {
        setSelectedAgent(agent);
        formDialog.onTrue();
      }
      handleCloseMenu();
    },
    [agents, formDialog]
  );

  // Handle opening the menu
  const handleOpenMenu = useCallback((event: React.MouseEvent<HTMLButtonElement>, id: string) => {
    event.stopPropagation();
    setMenuPosition({
      left: event.clientX,
      top: event.clientY,
    });
    setSelectedId(id);
  }, []);

  // Handle closing the menu
  const handleCloseMenu = useCallback(() => {
    setMenuPosition(null);
  }, []);

  // Handle viewing an agent
  const handleViewAgent = useCallback((id: string) => {
    // Implement view functionality here
    console.log(`Viewing agent with ID: ${id}`);
    handleCloseMenu();
  }, []);

  // Handle opening the confirm dialog for deleting agents
  const handleOpenConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(true);
  }, []);

  // Handle closing the confirm dialog
  const handleCloseConfirmDialog = useCallback(() => {
    setOpenConfirmDialog(false);
  }, []);

  // Handle deleting selected agents
  const handleDeleteAgents = useCallback(() => {
    setAgents((prev) => prev.filter((agent) => !selectedIds.includes(agent.id)));
    table.setSelected([]);
    setSelectedIds([]);
    setOpenConfirmDialog(false);
  }, [selectedIds, table]);

  // Handle deleting a single agent from menu
  const handleDeleteSingleAgent = useCallback(() => {
    if (selectedId) {
      setAgents((prev) => prev.filter((agent) => agent.id !== selectedId));
      setOpenConfirmDialog(false);
    }
    handleCloseMenu();
  }, [selectedId]);

  // Handle selecting a row
  const handleSelectRow = useCallback(
    (row: AgentData, table: any) => {
      table.onSelectRow(row?.id);

      // Update selectedIds based on table.selected
      if (table.selected.includes(row?.id)) {
        setSelectedIds((prev) => [...prev, row?.id]);
      } else {
        setSelectedIds((prev) => prev.filter((selectedId) => selectedId !== row?.id));
      }
    },
    [table]
  );

  // Handle selecting all rows
  const handleSelectAllRows = useCallback(
    (ids: string[]) => (checked: boolean) => {
      table.onSelectAllRows(checked, ids);

      // Update selectedIds based on checked state
      if (checked) {
        setSelectedIds(ids);
      } else {
        setSelectedIds([]);
      }
    },
    [table]
  );

  // Handle form submission
  const handleFormSubmit = useCallback(
    (data: AgentFormValues) => {
      if (selectedAgent) {
        // Edit existing agent
        setAgents((prev) =>
          prev.map((agent) => (agent.id === selectedAgent.id ? { ...agent, ...data } : agent))
        );
      } else {
        // Add new agent
        const newAgent = {
          id: String(agents.length + 1),
          avatarUrl: '/assets/images/avatar/avatar_default.jpg', // Default avatar
          ...data,
        } as AgentData;
        setAgents((prev) => [...prev, newAgent]);
      }
      formDialog.onFalse();
    },
    [agents, formDialog, selectedAgent]
  );

  const MENU_OPTIONS = (row: AgentData) => [
    {
      label: 'view',
      icon: 'eva:eye-fill',
      onClick: () => handleViewAgent(row.id),
      color: 'inherit',
    },
    {
      label: 'edit',
      icon: 'eva:edit-fill',
      onClick: () => handleEditAgent(row.id),
      color: 'primary.main',
    },
    {
      label: 'delete',
      icon: 'eva:trash-2-outline',
      onClick: () => {
        setSelectedId(row.id);
        handleOpenConfirmDialog();
      },
      color: 'error.main',
    },
  ];

  // Table columns configuration
  const columns: AppTablePropsType<AgentData>['columns'] = [
    {
      name: 'name',
      PreviewComponent: (data) => {
        const { avatarUrl, name, description, email } = data;
        return (
          <Stack direction="row" alignItems="center" spacing={2}>
            <Avatar src={avatarUrl} alt={name} />
            <Stack spacing={0.5} sx={{ maxWidth: 240 }}>
              <Typography variant="subtitle2" noWrap>
                {name}
              </Typography>

              <Typography variant="caption" sx={{ color: 'text.disabled' }} noWrap>
                {description}
              </Typography>

              <Typography variant="caption" sx={{ color: 'text.secondary' }} noWrap>
                {email}
              </Typography>
            </Stack>
          </Stack>
        );
      },
    },
    {
      name: 'category',
      PreviewComponent: (data) => {
        const { category, status } = data;
        return (
          <Stack spacing={1}>
            <Label
            width={90}
              variant="soft"
              color={
                (category === 'Sales' && 'primary') ||
                (category === 'Support' && 'info') ||
                (category === 'Marketing' && 'warning') ||
                (category === 'HR' && 'success') ||
                (category === 'IT' && 'secondary') ||
                (category === 'UX' && 'error') ||
                (category === 'Social Media' && 'info') ||
                'default'
              }
            >
              {category}
            </Label>

          </Stack>
        );
      },
    },
    {
      name: 'id',
      cellSx: { width: '50px' },
      PreviewComponent: (row) => {
        return <LongMenu options={MENU_OPTIONS(row)} />;
      },
    },
  ];

  // Handle status tab change
  const handleStatusTabChange = useCallback((_event: React.SyntheticEvent, newValue: string) => {
    setStatusTab(newValue);
  }, []);

  // Handle category filter change
  const handleCategoryChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setCategoryFilter(event.target.value);
  }, []);

  // Handle search query change
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  }, []);

  // Calculate category counts and generate category options with counts
  const categoryOptions = useMemo(() => {
    // Count agents by category
    const categoryCounts: Record<string, number> = {};

    // Filter agents by status tab first
    const statusFilteredAgents = agents.filter(agent =>
      statusTab === 'all' || agent.status === statusTab
    );

    // Count total agents after status filtering
    const totalCount = statusFilteredAgents.length;

    // Count by category
    statusFilteredAgents.forEach(agent => {
      if (categoryCounts[agent.category]) {
        categoryCounts[agent.category] += 1;
      } else {
        categoryCounts[agent.category] = 1;
      }
    });

    // Generate options with counts
    const options = [
      { value: 'all', label: 'All Categories', count: totalCount },
      ...Object.keys(categoryCounts).map(category => ({
        value: category,
        label: category,
        count: categoryCounts[category]
      })).sort((a, b) => a.label.localeCompare(b.label))
    ];

    return options;
  }, [agents, statusTab]);

  // Filter agents based on status tab, category filter, and search query
  const filteredAgents = useMemo(() => {
    return agents.filter((agent) => {
      // Filter by status tab
      if (statusTab !== 'all' && agent.status !== statusTab) {
        return false;
      }

      // Filter by category
      if (categoryFilter !== 'all' && agent.category !== categoryFilter) {
        return false;
      }

      // Filter by search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        return (
          agent.name.toLowerCase().includes(query) ||
          agent.description.toLowerCase().includes(query) ||
          agent.email.toLowerCase().includes(query)
        );
      }

      return true;
    });
  }, [agents, statusTab, categoryFilter, searchQuery]);

  return {
    // State
    agents,
    filteredAgents,
    selectedAgent,
    openConfirmDialog,
    selectedIds,
    menuPosition,
    selectedId,
    formDialog,
    openMenu,
    table,
    columns,
    statusTab,
    categoryFilter,
    searchQuery,
    categoryOptions,
    setOpenConfirmDialog,
    setAgents,
    // Handlers
    handleAddAgent,
    handleEditAgent,
    handleOpenMenu,
    handleCloseMenu,
    handleViewAgent,
    handleOpenConfirmDialog,
    handleCloseConfirmDialog,
    handleDeleteAgents,
    handleDeleteSingleAgent,
    handleSelectRow,
    handleSelectAllRows,
    handleFormSubmit,
    handleStatusTabChange,
    handleCategoryChange,
    handleSearchChange,
  };
}
