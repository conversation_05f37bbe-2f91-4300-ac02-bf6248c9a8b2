import { useState, useRef, useEffect } from 'react';
import {
  Box,
  Card,
  Grid,
  Avatar,
  Typography,
  LinearProgress,
  IconButton,
  styled,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

const SCROLL_SPEED = 400; // pixels to scroll per click

const CarouselArrowButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  transform: 'translateY(-50%)',
  zIndex: 9,
  width: 36,
  height: 36,
  borderRadius: '50%',
  backgroundColor: theme.palette.background.paper,
  boxShadow: theme.customShadows.z8,
  color: '#FF6B35',
  '&:hover': {
    backgroundColor: theme.palette.background.paper,
    color: '#E55A2A',
  },
  [theme.breakpoints.down('sm')]: {
    width: 30,
    height: 30,
  },
}));

// ----------------------------------------------------------------------

type CloudPlatform = {
  id: string;
  name: string;
  icon: string;
  files: number;
  color: string;
  progress: number;
};

type CloudPlatformsCarouselProps = {
  platforms: CloudPlatform[];
};

export default function CloudPlatformsCarousel({ platforms }: CloudPlatformsCarouselProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [maxScroll, setMaxScroll] = useState(0);

  // Calculate maximum scroll width on mount and window resize
  useEffect(() => {
    const updateScrollInfo = () => {
      if (scrollRef.current) {
        const { scrollWidth, clientWidth } = scrollRef.current;
        setMaxScroll(scrollWidth - clientWidth);

        // Update arrow visibility based on current position
        setShowLeftArrow(scrollPosition > 0);
        setShowRightArrow(scrollPosition < scrollWidth - clientWidth - 10);
      }
    };

    updateScrollInfo();
    window.addEventListener('resize', updateScrollInfo);

    return () => {
      window.removeEventListener('resize', updateScrollInfo);
    };
  }, [scrollPosition]);

  const handleScrollLeft = () => {
    if (scrollRef.current) {
      const newPosition = Math.max(0, scrollPosition - SCROLL_SPEED);
      scrollRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth',
      });
      setScrollPosition(newPosition);
    }
  };

  const handleScrollRight = () => {
    if (scrollRef.current) {
      const newPosition = Math.min(maxScroll, scrollPosition + SCROLL_SPEED);
      scrollRef.current.scrollTo({
        left: newPosition,
        behavior: 'smooth',
      });
      setScrollPosition(newPosition);
    }
  };

  const handleScroll = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setScrollPosition(scrollLeft);
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  return (
    <Box sx={{ position: 'relative', mb: 4 }}>
      <CarouselArrowButton
        onClick={handleScrollLeft}
        sx={{
          left: { xs: -10, sm: -15, md: -18 },
          boxShadow: (theme) => theme.customShadows.z8,
          zIndex: 10,
          opacity: showLeftArrow ? 1 : 0.5,
          pointerEvents: showLeftArrow ? 'auto' : 'none',
        }}
        aria-label="previous platforms"
        disabled={!showLeftArrow}
      >
        <Iconify icon="eva:arrow-ios-back-fill" width={20} />
      </CarouselArrowButton>

      <CarouselArrowButton
        onClick={handleScrollRight}
        sx={{
          right: { xs: -10, sm: -15, md: -18 },
          boxShadow: (theme) => theme.customShadows.z8,
          zIndex: 10,
          opacity: showRightArrow ? 1 : 0.5,
          pointerEvents: showRightArrow ? 'auto' : 'none',
        }}
        aria-label="next platforms"
        disabled={!showRightArrow}
      >
        <Iconify icon="eva:arrow-ios-forward-fill" width={20} />
      </CarouselArrowButton>

      <Box
        ref={scrollRef}
        onScroll={handleScroll}
        sx={{
          display: 'flex',
          overflowX: 'auto',
          gap: 3,
          py: 1,
          px: { xs: 2, sm: 3, md: 4 },
          scrollbarWidth: 'none', // Firefox
          msOverflowStyle: 'none', // IE/Edge
          '&::-webkit-scrollbar': {
            // Chrome/Safari/Opera
            display: 'none',
          },
        }}
      >
        {platforms.map((platform) => (
          <Card
            key={platform.id}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              p: 2,
              boxShadow: 'none',
              border: '1px solid #f0f0f0',
              minWidth: { xs: 150, sm: 170, md: 190 },
              maxWidth: { xs: 150, sm: 170, md: 190 },
              height: 160,
              flexShrink: 0,
              cursor: 'pointer',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-4px)',
                boxShadow: (theme) => theme.customShadows.z8,
                borderColor: '#FF6B35',
              },
            }}
          >
            <Avatar
              src={platform.icon}
              sx={{ width: 40, height: 40, mb: 1, bgcolor: 'transparent' }}
            />
            <Typography variant="subtitle2" sx={{ mb: 0.5 }}>
              {platform.name}
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ mb: 1 }}>
              {platform.files} files
            </Typography>
            <Box sx={{ width: '100%', mt: 'auto' }}>
              <LinearProgress
                variant="determinate"
                value={platform.progress}
                sx={{
                  height: 4,
                  borderRadius: 2,
                  bgcolor: '#f5f5f5',
                  '& .MuiLinearProgress-bar': {
                    bgcolor: '#FF6B35',
                    borderRadius: 2,
                  },
                }}
              />
            </Box>
          </Card>
        ))}
      </Box>
    </Box>
  );
}
