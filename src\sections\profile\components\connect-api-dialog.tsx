import { useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  DialogContent,
  DialogActions,
  Typography,
  Box,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Stack,
  TextField,
} from '@mui/material';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';
import { useTranslation } from 'react-i18next';

const schema = z.object({
  url: z.string().url('Invalid URL'),
  method: z.string().min(1, 'Method is required'),
  authType: z.string().min(1, 'Auth type is required'),
});

type FormValues = z.infer<typeof schema>;

interface Header {
  id: string;
  key: string;
  value: string;
  isEditing: boolean;
}

export default function ConnectAPIDialog({ open, onClose }: { open: boolean; onClose: () => void }) {
  const { t } = useTranslation();
  const [headers, setHeaders] = useState<Header[]>([]);

  const methods = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      url: '',
      method: 'GET',
      authType: 'None',
    },
  });

  const { handleSubmit } = methods;

  const onSubmit = (data: FormValues) => {
    console.log('Submitted:', data, headers);
    onClose();
  };

  const handleAddHeader = () => {
    setHeaders([...headers, {
      id: `header-${Date.now()}`,
      key: '',
      value: '',
      isEditing: true,
    }]);
  };

  const handleRemoveHeader = (id: string) => {
    setHeaders(headers.filter(h => h.id !== id));
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md">
      <DialogTitle sx={{ textAlign: 'center' }}>
        <Typography variant="h3">{t('connectApiDialog.title')}</Typography>
      </DialogTitle>
      
      <DialogContent>
        <Form methods={methods} onSubmit={handleSubmit(onSubmit)}>
          <Stack spacing={3}>
            <Field.Text 
              name="url" 
              label={t('connectApiDialog.urlLabel')} 
              placeholder={t('connectApiDialog.urlPlaceholder')}
            />

            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>{t('connectApiDialog.headerKey')}</TableCell>
                    <TableCell>{t('connectApiDialog.headerValue')}</TableCell>
                    <TableCell>{t('connectApiDialog.actions')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {headers.map((header) => (
                    <TableRow key={header.id}>
                      <TableCell>
                        <TextField
                          value={header.key}
                          onChange={(e) => setHeaders(headers.map(h => 
                            h.id === header.id ? {...h, key: e.target.value} : h
                          ))}
                          size="small"
                          fullWidth
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          value={header.value}
                          onChange={(e) => setHeaders(headers.map(h => 
                            h.id === header.id ? {...h, value: e.target.value} : h
                          ))}
                          size="small"
                          fullWidth
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton onClick={() => handleRemoveHeader(header.id)}>
                          <Iconify icon="mdi:delete" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <Button 
              startIcon={<Iconify icon="eva:plus-fill" />}
              onClick={handleAddHeader}
            >
              {t('connectApiDialog.addHeader')}
            </Button>
          </Stack>

          <DialogActions>
            <Button onClick={onClose}>
              {t('connectApiDialog.cancel')}
            </Button>
            <Button type="submit" variant="contained">
              {t('connectApiDialog.connect')}
            </Button>
          </DialogActions>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
