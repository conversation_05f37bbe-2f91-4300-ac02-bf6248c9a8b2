import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Stack,
  IconButton,
  Typography,
  Stepper,
  Step,
  StepLabel,
  Box,
  Grid,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Iconify } from 'src/components/iconify';
import { Field } from 'src/components/hook-form/fields';
import { Form } from 'src/components/hook-form/form-provider';
import { AppButton } from 'src/components/common';
import { useTeamForm, TeamFormValues } from './use-team-form';
import { TeamMember } from '../view/use-teams-view';
import { ResourcesStep } from './components';
import AiStep from './components/ai-step';
import MembersStep from './components/members-step';

// Component props
interface TeamFormProps {
  open: boolean;
  onClose: () => void;
  team: {
    id: string;
    name: string;
    description: string;
    type: string;
    members: TeamMember[];
    createdAt: Date;
  } | null;
  availableMembers: TeamMember[];
  onSubmit: (data: TeamFormValues) => void;
}

export default function TeamForm({
  open,
  onClose,
  team,
  availableMembers,
  onSubmit,
}: TeamFormProps) {
  const { t } = useTranslation();
  const {
    activeStep,
    methods,
    selectedMemberIds,
    isSubmitting,
    handleNext,
    handleBack,
    handleMemberIdChange,
    onFormSubmit,
    handleSubmit,
  } = useTeamForm({ team, availableMembers, onSubmit });

  const steps = [
    {
      label: t('components.teams.form.steps.teamInfo'),
      icon: 'mdi:file-document',
      fields: (
        <Stack spacing={3}>
          <Field.Text name="name" label={t('components.teams.form.fields.teamName')} />
          <Field.Text name="description" rows={15} label={t('components.teams.form.fields.description')} multiline />
        </Stack>
      ),
    },
    {
      label: t('components.teams.form.steps.resources'),
      icon: 'lucide:database',
      fields: <ResourcesStep />,
    },
    {
      label: t('components.teams.form.steps.aiModel'),
      icon: 'hugeicons:google-gemini',
      fields: <AiStep />,
    },
    {
      label: t('components.teams.form.steps.members'),
      icon: 'mdi:account-group',
      fields: (
        <MembersStep selectedMembers={selectedMemberIds} onMemberChange={handleMemberIdChange} />
      ),
    },
    {
      label: t('components.teams.form.steps.instructions'),
      icon: 'material-symbols:code-rounded',
      fields: (
        <Stack spacing={3}>
          <Field.Instruction
            name="instructions"
            label={t('components.teams.form.fields.instructions')}
            rows={13}
          />
        </Stack>
      ),
    },
  ];

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="lg">
      <DialogTitle sx={{ pb: 2 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h3">{team ? t('components.teams.form.editTeam') : t('components.teams.form.createNewTeam')}</Typography>

          <IconButton onClick={onClose}>
            <Iconify icon="material-symbols:cancel-outline" />
          </IconButton>
        </Stack>
      </DialogTitle>
      <Box sx={{ pb: 4, px: 3.5 }}>
        <Typography variant="body2">{t('components.teams.form.formInstructions')}</Typography>
      </Box>
      <Form methods={methods} onSubmit={handleSubmit(onFormSubmit)}>
        <DialogContent sx={{ border: '1px solid rgba(0, 0, 0, 0.08)' }}>
          <Grid container spacing={0}>
            <Grid item xs={12} sx={{ borderRight: '1px solid rgba(0, 0, 0, 0.08)' ,pt:'56px',px:'20px'}} md={3}>
              <Stepper
                activeStep={activeStep}
                orientation="vertical"
                sx={{
                  '& .MuiStepConnector-root': {
                    marginLeft: '12px',
                    '& .MuiStepConnector-line': {
                      minHeight: '32px',
                    },
                  },
                  '& .MuiStepConnector-line': {
                    borderLeftWidth: 2,
                  },
                }}
              >
                {steps.map((step, index) => {
                  const isCompleted = index < activeStep;
                  const isActive = index === activeStep;
                  return (
                    <Step
                      key={step.label}
                      completed={isCompleted}
                      sx={{
                        '& .MuiStepContent-root': {
                          borderColor: isCompleted
                            ? 'success.main'
                            : isActive
                              ? 'success.main'
                              : 'divider',
                          ml: '12px',
                        },
                        '& .MuiStepLabel-root': {
                          '& .MuiStepIcon-root.Mui-completed': {
                            color: 'success.main',
                          },
                        },
                        '& .MuiStepConnector-root .MuiStepConnector-line': {
                          border: isCompleted ? 'success.main' : 'divider',
                        },
                      }}
                    >
                      <StepLabel>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Iconify
                            icon={step.icon}
                            width={24}
                            height={24}
                            sx={{
                              color: isCompleted
                                ? 'primary.main'
                                : isActive
                                  ? 'primary.main'
                                  : 'text.secondary',
                            }}
                          />
                          <Typography
                            variant="subtitle1"
                            sx={{
                              color: isCompleted
                                ? 'primary.main'
                                : isActive
                                  ? 'primary.main'
                                  : 'text.primary',
                              fontWeight: isActive || isCompleted ? 600 : 400,
                            }}
                          >
                            {step.label}
                          </Typography>
                        </Stack>
                      </StepLabel>
                    </Step>
                  );
                })}
              </Stepper>
            </Grid>

            <Grid item xs={12} md={9}>
              <Box sx={{ p: 1 }}>{steps[activeStep].fields}</Box>
              <DialogActions>
                <Stack
                  direction="row"
                  spacing={2}
                  sx={{ width: '100%', justifyContent: 'space-between', pb: 2, px: 2 }}
                >
                  <Stack direction="row" alignItems="center" spacing={1}>
                    {activeStep > 0 && (
                      <AppButton
                        label={t('components.buttons.previous')}
                        variant="outlined"
                        color="inherit"
                        onClick={handleBack}
                      />
                    )}
                  </Stack>

                  <Stack direction="row" spacing={2}>
                    {activeStep < steps.length - 1 ? (
                      <AppButton
                        label={t('components.buttons.next')}
                        variant="contained"
                        color="primary"
                        onClick={handleNext}
                      />
                    ) : (
                      <AppButton
                        label={team ? t('components.buttons.update') : t('components.buttons.create')}
                        type="submit"
                        variant="contained"
                        color="primary"
                        isLoading={isSubmitting}
                      />
                    )}
                  </Stack>
                </Stack>
              </DialogActions>
            </Grid>
          </Grid>
        </DialogContent>
      </Form>
    </Dialog>
  );
}
