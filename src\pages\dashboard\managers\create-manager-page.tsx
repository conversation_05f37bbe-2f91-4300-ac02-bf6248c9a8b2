import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { useManagersView } from 'src/sections/managers/view/use-managers-view';
import ManagerForm from 'src/sections/managers/form/manager-form';
import { Box } from '@mui/material';

// ----------------------------------------------------------------------

export default function CreateManagerPage() {
  const { t } = useTranslation();
  // Use the managers view hook to get the form submission handler
  const { handleFormSubmit } = useManagersView([]);

  return (
    <>
      <Helmet>
        <title>{`${t('pages.dashboard.title')}: ${t('pages.dashboard.createManager')}`}</title>
      </Helmet>

      <Box m='100px' mt='0px'>
        <ManagerForm onSubmit={handleFormSubmit} />
      </Box>
    </>
  );
}
