import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { ManagerData } from '../view/use-managers-view';

// Form validation schema
const managerSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  username: z.string().min(3, 'Username must be at least 3 characters').max(20, 'Username must be at most 20 characters'),
  email: z.string().email('Email must be a valid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .optional()
    .or(z.literal('')),
  confirmPassword: z.string().optional().or(z.literal('')),
}).refine((data) => {
  // If password is provided, confirmPassword must match
  if (data.password && data.password !== data.confirmPassword) {
    return false;
  }
  return true;
}, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

// Form values type
export type ManagerFormValues = z.infer<typeof managerSchema>;

interface UseManagerFormProps {
  manager?: ManagerData | null;
  onSubmit: (data: ManagerFormValues, id?: string) => void;
}

export function useManagerForm({ manager, onSubmit }: UseManagerFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with default values or manager data for editing
  const methods = useForm<ManagerFormValues>({
    mode: 'onChange',
    resolver: zodResolver(managerSchema),
    defaultValues: manager
      ? {
          firstName: manager.firstName,
          lastName: manager.lastName,
          username: manager.username,
          email: manager.email,
          password: '',
          confirmPassword: '',
        }
      : {
          firstName: '',
          lastName: '',
          username: '',
          email: '',
          password: '',
          confirmPassword: '',
        },
  });

  const { handleSubmit, formState } = methods;

  // Handle form submission
  const onFormSubmit = async (data: ManagerFormValues) => {
    setIsSubmitting(true);
    try {
      // Submit form data
      onSubmit(data, manager?.id);
      
      // Navigate back to managers list
      router.push(paths.dashboard.managers.root);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    router.push(paths.dashboard.managers.root);
  };

  return {
    methods,
    isSubmitting: isSubmitting || formState.isSubmitting,
    onFormSubmit,
    handleSubmit,
    handleCancel,
  };
}
