import { useState, useEffect } from 'react';
import { 
  Autocomplete, 
  TextField, 
  InputAdornment, 
  SxProps, 
  Theme,
  Typography,
  Box,
  ListItem,
  ListItemText,
  Avatar,
  AutocompleteChangeReason
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import parse from 'autosuggest-highlight/parse';
import match from 'autosuggest-highlight/match';

interface AgentSearchBarProps {
  query: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  sx?: SxProps<Theme>;
  options?: AgentOption[];
  onOptionSelect?: (option: AgentOption | null) => void;
}

export interface AgentOption {
  id: string;
  name: string;
  description?: string;
  email?: string;
  avatarUrl?: string;
  category?: string;
  status?: string;
}

export default function AgentSearchBar({
  query,
  onChange,
  placeholder = 'Search agents...',
  sx,
  options = [],
  onOptionSelect,
}: AgentSearchBarProps) {
  const [inputValue, setInputValue] = useState(query);
  const [selectedOption, setSelectedOption] = useState<AgentOption | null>(null);

  // Update local state when query prop changes
  useEffect(() => {
    setInputValue(query);
  }, [query]);

  // Handle input change
  const handleInputChange = (event: React.SyntheticEvent, newInputValue: string) => {
    setInputValue(newInputValue);
    
    // Create a synthetic event to pass to the onChange handler
    const syntheticEvent = {
      target: {
        value: newInputValue
      }
    } as React.ChangeEvent<HTMLInputElement>;
    
    onChange(syntheticEvent);
  };

  // Handle option selection
  const handleOptionSelect = (
    _event: React.SyntheticEvent,
    value: string | AgentOption | null,
    _reason: AutocompleteChangeReason
  ) => {
    if (typeof value === 'string') {
      // Handle free text input case
      setSelectedOption(null);
      if (onOptionSelect) {
        onOptionSelect(null);
      }
      return;
    }

    // Handle AgentOption case
    setSelectedOption(value);
    
    if (onOptionSelect) {
      onOptionSelect(value);
    }

    if (value) {
      const syntheticEvent = {
        target: {
          value: value.name
        }
      } as React.ChangeEvent<HTMLInputElement>;
      
      onChange(syntheticEvent);
    }
  };

  return (
    <Autocomplete
      fullWidth
      freeSolo
      options={options}
      getOptionLabel={(option) => typeof option === 'string' ? option : option.name}
      inputValue={inputValue}
      onInputChange={handleInputChange}
      onChange={handleOptionSelect}
      renderInput={(params) => (
        <TextField
          {...params}
          fullWidth
          placeholder={placeholder}
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 1,
            },
          }}
        />
      )}
      renderOption={(props, option, { inputValue }) => {
        // Skip rendering if option is a string (from freeSolo)
        if (typeof option === 'string') {
          return null;
        }

        const matches = match(option.name, inputValue);
        const parts = parse(option.name, matches);

        return (
          <ListItem {...props} key={option.id}>
            {option.avatarUrl && (
              <Avatar 
                src={option.avatarUrl} 
                alt={option.name}
                sx={{ mr: 2, width: 40, height: 40 }}
              />
            )}
            <ListItemText
              primary={
                <Box component="span">
                  {parts.map((part, index) => (
                    <Typography
                      key={index}
                      component="span"
                      sx={{ fontWeight: part.highlight ? 'bold' : 'regular' }}
                      color={part.highlight ? 'primary.main' : 'text.primary'}
                    >
                      {part.text}
                    </Typography>
                  ))}
                </Box>
              }
              secondary={option.description || option.email}
            />
            {option.category && (
              <Typography 
                variant="caption" 
                color="text.secondary"
                sx={{ 
                  ml: 1, 
                  bgcolor: 'background.neutral',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1
                }}
              >
                {option.category}
              </Typography>
            )}
          </ListItem>
        );
      }}
      sx={{
        ...sx,
      }}
    />
  );
}
