import type { Theme, SxProps } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { CONFIG } from 'src/config-global';

export type AuthSplitLayoutProps = {
  sx?: SxProps<Theme>;
  children: React.ReactNode;
  section?: {
    title?: string;
    subtitle?: string;
  };
  isForgotPassword?: boolean;
  isRegisterPage?: boolean;
  isLoginPage?: boolean;
};

export function AuthSplitLayout({
  sx,
  children,
  section,
  isForgotPassword,
  isRegisterPage,
  isLoginPage,
}: AuthSplitLayoutProps) {
  if (isForgotPassword) {
    return (

      <>
      <Box
        sx={{
          minHeight: '100vh',
          display: 'grid',

          '&::after': {
            content: '""',
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            height: '150vh',
            backgroundImage: `url(${CONFIG.site.basePath}/assets/background/bg-new-passord.png)`,
            backgroundSize: '100%',
            backgroundPosition: 'center',
            width: '100%',
            // zIndex: -1,
            pointerEvents: 'none', // This ensures clicks pass through to elements below
          },
          ...sx,
        }}
        >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'background.default',
            position: 'relative',
            // zIndex: 2,
              }}
          >
          {children}
        </Box>
      </Box>
    </>
    )
  }
  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'grid',
        gridTemplateColumns: { xs: '1fr', md: '6fr 6fr' },
        ...sx,
      }}
    >
      {isLoginPage ? (
        // For login page, content on the right side, background on the left
        <>
          <Box
            sx={{
              backgroundImage: `url(${CONFIG.site.basePath}/assets/background/bg-auth-page.svg)`,
              display: { xs: 'none', md: 'flex' },
              flexDirection: 'column',
              justifyContent: 'flex-end',
              backgroundRepeat: 'repeat',
              borderRadius: '24px',
              backgroundSize: 'cover',
              width: '100%',
              height:'100vh',
              backgroundPositionY: 'center',
              m: '18px',
              position: 'relative',
              alignItems: 'center',
            }}
          >
            <Box
              sx={{
                height: 'auto',
                width: '90%',
                left: '32px',
                p: 3,
                m: 3,
                borderRadius: '24px',
                bgcolor: '#421A0CB2',
                backdropFilter: 'blur(4px)',
              }}
            >
              <Box sx={{ color: 'white' }}>
                <Typography variant="h2" sx={{ mb: 2 }}>
                  {section?.title || ''}
                </Typography>
                <Typography>{section?.subtitle || ''}</Typography>
              </Box>
            </Box>
          </Box>

          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'background.default',
            }}
          >
            {children}
          </Box>
        </>
      ) : isRegisterPage ? (
        // For register page, content on the left side, background on the right
        <>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'background.default',
            }}
          >
            {children}
          </Box>

          <Box
            sx={{
              backgroundImage: `url(${CONFIG.site.basePath}/assets/background/bg-auth-page.svg)`,
              backgroundPositionY: 'center',
              display: { xs: 'none', md: 'flex' },
              flexDirection: 'column',
              justifyContent: 'flex-end',
              backgroundRepeat: 'no-repeat',
              borderRadius: '24px',
              backgroundSize: 'cover',
              m: '18px 0px',
              width: '98%',
              alignItems: 'center',
            }}
          >
            <Box
              sx={{
                height: 'auto',
                width: '90%',
                left: '32px',
                p: 3,
                m: 3,
                borderRadius: '24px',
                bgcolor: '#421A0CB2',
                backdropFilter: 'blur(4px)',
              }}
            >
              <Box sx={{ color: 'white' }}>
                <Typography variant="h3" sx={{ mb: 2 }}>
                  {section?.title || ''}
                </Typography>
                <Typography>{section?.subtitle || ''}</Typography>
              </Box>
            </Box>
          </Box>
        </>
      ) : (
        // Default layout for other pages
        <>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'background.default',
            }}
          >
            {children}
          </Box>

          <Box
            sx={{
              backgroundImage: `url(${CONFIG.site.basePath}/assets/background/bg-auth-page.svg)`,
              backgroundPositionY: 'center',
              display: { xs: 'none', md: 'flex' },
              flexDirection: 'column',
              justifyContent: 'flex-end',
              backgroundRepeat: 'no-repeat',
              borderRadius: '24px',
              backgroundSize: 'cover',
              m: '18px 0px',
              width: '98%',
              alignItems: 'center',
            }}
          >
            <Box
              sx={{
                height: 'auto',
                width: '90%',
                left: '32px',
                p: 3,
                m: 3,
                borderRadius: '24px',
                bgcolor: '#421A0CB2',
                backdropFilter: 'blur(4px)',
              }}
            >
              <Box sx={{ color: 'white' }}>
                <Typography variant="h3" sx={{ mb: 2 }}>
                  {section?.title || ''}
                </Typography>
                <Typography>{section?.subtitle || ''}</Typography>
              </Box>
            </Box>
          </Box>
        </>
      )}
    </Box>
  );
}
