import { z as zod } from 'zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslation } from 'react-i18next';

import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import InputAdornment from '@mui/material/InputAdornment';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';

import { AppButton } from 'src/components/common';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';
import { RouterLink } from 'src/routes/components';

import { useBoolean } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

import { useAuthContext } from 'src/auth/hooks';
import { signInWithPassword } from 'src/auth/context/jwt';
import { CONFIG } from 'src/config-global';
import { Avatar, useTheme } from '@mui/material';

// ----------------------------------------------------------------------

export const SignInSchema = zod.object({
  email: zod
    .string()
    .min(1, { message: 'Email is required!' })
    .email({ message: 'Email must be a valid email address!' }),
  password: zod
    .string()
    .min(1, { message: 'Password is required!' })
    .min(6, { message: 'Password must be at least 6 characters!' }),
});

export type SignInSchemaType = zod.infer<typeof SignInSchema>;

// ----------------------------------------------------------------------

export function JwtSignInView() {
  const router = useRouter();
  const { checkUserSession } = useAuthContext();
  const [errorMsg, setErrorMsg] = useState('');
  const password = useBoolean();
  const { t } = useTranslation();
  const theme = useTheme();
  const methods = useForm<SignInSchemaType>({
    resolver: zodResolver(SignInSchema),
    defaultValues: {
      email: '<EMAIL>',
      password: '@demo1',
    },
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      await signInWithPassword({ email: data.email, password: data.password });
      await checkUserSession?.();
      router.push(paths.dashboard.root);
    } catch (error) {
      console.error(error);
      setErrorMsg(error instanceof Error ? error.message : error);
    }
  });

  return (
    <Box sx={{ width: '50%' }}>
      <Box textAlign="end" sx={{ display: 'felx', justifyContent: 'center', alignItems: 'center' }}>
        <Box
          component="img"
          src={`${CONFIG.site.basePath}/logo/logo-single.svg`}
          sx={{ width: 53.53, height: 50, mb: 5 }}
        />
      </Box>

      <Typography variant="h1" paragraph textAlign="center">
        {t('auth.login')}
      </Typography>

      <Typography variant="body1" textAlign="center" sx={{ color: 'text.secondary', mb: 5 }}>
        {t('auth.welcomeSubtitle')}
      </Typography>

      {!!errorMsg && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {errorMsg}
        </Alert>
      )}

      <Form methods={methods} onSubmit={onSubmit}>
        <Stack spacing={3} sx={{ width: '100%' }}>
          <Field.Text
            name="email"
            label={t('auth.email')}
            placeholder={t('auth.enterYourEmail')}
            fullWidth
          />

          <Field.Text
            name="password"
            label={t('auth.password')}
            type={password.value ? 'text' : 'password'}
            placeholder={t('auth.enterYourPassword')}
            fullWidth
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={password.onToggle} edge="end">
                    <Iconify icon={password.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
          <Link
            component={RouterLink}
            href={paths.auth.jwt.forgotPassword}
            variant="body2"
            color="inherit"
            sx={{ alignSelf: 'flex-end' }}
          >
            {t('auth.forgotPassword')}
          </Link>

          <AppButton
            label={t('auth.login')}
            fullWidth
            color="primary"
            size="large"
            type="submit"
            variant="contained"
            isLoading={isSubmitting}
          />

          <Stack direction="row" alignItems="center" spacing={2} sx={{ my: 1 }}>
            <Divider sx={{ flexGrow: 1 }}>{t('auth.orLoginWith')}</Divider>
          </Stack>

          <Stack direction="row" spacing={2} justifyContent="center">
            <Iconify
              icon="mdi:apple"
              style={{
                color: theme.palette.mode === 'dark' ? 'white' : 'black',
                width: '32px',
                height: '32px',
              }}
            />

            <Iconify
              icon="fontisto:facebook"
              style={{
                color: theme.palette.mode === 'dark' ? 'white' : 'black',
                width: '32px',
                height: '32px',
              }}
            />

            <Iconify
              icon="ri:google-fill"
              style={{
                color: theme.palette.mode === 'dark' ? 'white' : 'black',
                width: '32px',
                height: '32px',
              }}
            />
          </Stack>

          <Typography variant="body2" align="center" sx={{ color: 'text.secondary' }}>
            {t('auth.alreadyHaveAccount')}{' '}
            <Link component={RouterLink} href={paths.auth.jwt.signUp} variant="subtitle2">
              {t('auth.register')}
            </Link>
          </Typography>
        </Stack>
      </Form>

      <Stack direction="row" spacing={1} justifyContent="center" alignItems="center" sx={{ mt: 3 }}>
        <Link variant="body2" color="text.secondary" href="#">
          Privacy policy
        </Link>
        <Typography color="text.disabled" sx={{ fontSize: '30px' }}>
          •
        </Typography>
        <Link variant="body2" color="text.secondary" href="#">
          Terms of use
        </Link>
        <Typography color="text.disabled" sx={{ fontSize: '30px' }}>
          •
        </Typography>
        <Link variant="body2" color="text.secondary" href="#">
          DMCA
        </Link>
      </Stack>
    </Box>
  );
}
