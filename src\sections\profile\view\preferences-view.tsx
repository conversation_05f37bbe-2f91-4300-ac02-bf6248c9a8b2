import { Box, Typography, Card } from '@mui/material';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { paths } from 'src/routes/paths';

// ----------------------------------------------------------------------

export function PreferencesView() {
  return (
    <>
      <CustomBreadcrumbs
        heading="Preferences"
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'Profile', href: paths.dashboard.profile.root },
          { name: 'Preferences' },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card sx={{ p: 3 }}>
        <Box sx={{ textAlign: 'center', py: 5 }}>
          <Typography variant="h6" paragraph>
            Preferences
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            This section will be implemented in the future.
          </Typography>
        </Box>
      </Card>
    </>
  );
}
