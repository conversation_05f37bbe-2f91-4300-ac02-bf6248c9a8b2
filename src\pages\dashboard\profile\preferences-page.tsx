import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { PreferencesView } from 'src/sections/profile/view/preferences-view';

// ----------------------------------------------------------------------

export default function PreferencesPage() {
  const { t } = useTranslation();

  return (
    <>
      <Helmet>
        <title>{`${t('pages.dashboard.title')}: ${t('pages.dashboard.preferences')}`}</title>
      </Helmet>

      <PreferencesView />
    </>
  );
}
